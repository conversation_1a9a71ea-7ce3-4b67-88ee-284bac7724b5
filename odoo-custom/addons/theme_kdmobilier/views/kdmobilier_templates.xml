<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <!-- WEB SALE PRODUCTS -->
  <template
    id="custom_products"
    inherit_id="website_sale.products"
    name="Custom Products"
  >
    <xpath expr="//div[@id='wrap']" position="replace">
      <div id="wrap" class="js_sale o_wsale_products_page">
        <!-- Category Banner Section -->

        <!-- Category Banner Section End-->
        <div
          class="oe_structure page-content oe_empty oe_structure_not_nearest"
          id="oe_structure_website_sale_products_1"
        />
        <div class="container-fluid px-4 oe_website_sale pt-2">
          <div
            class="row pbmit-element-posts-wrapper o_wsale_products_main_row align-items-start flex-nowrap"
          >
            <aside
              t-if="hasLeftColumn"
              id="products_grid_before"
              t-attf-class="sidebar position-sticky clearfix #{category and not category.child_id and attributes  and  'd-none d-lg-block col-2' or 'd-none'}"
            >
              <aside class="widget widget-categories">
                <span t-esc="opt_wsale_categories" />

                <div
                  class="o_wsale_products_grid_before_rail vh-100 ms-n2 mt-n2 pt-2 pe-lg-2 pb-lg-5 ps-2 overflow-y-scroll"
                >
                  <div
                    t-if="opt_wsale_categories"
                    class="products_categories mb-3"
                  >
                    <t t-call="website_sale.products_categories_list" />
                  </div>
                  <div class="products_attributes_filters" />
                  <t
                    t-if="opt_wsale_filter_price and opt_wsale_attributes"
                    t-call="website_sale.filter_products_price"
                  />
                </div>
              </aside>
            </aside>
            <div
              id="products_grid"
              t-attf-class="#{'o_wsale_layout_list' if layout_mode == 'list' else ''} #{category and not category.child_id and attributes and 'col-lg-10' or 'col-12'}"
            >
              <t t-call="theme_kdmobilier.custom_products_breadcrumb">
                <t
                  t-set="_classes"
                  t-valuef="d-none d-lg-flex w-100 p-0 small"
                />
              </t>
              <div
                class="products_header btn-toolbar flex-nowrap align-items-center justify-content-between gap-3"
              >
                <t
                  t-if="opt_wsale_categories_top"
                  t-call="theme_kdmobilier.custom_filmstrip_categories_2"
                />
                <!-- <t t-if="is_view_active('website_sale.search')" t-call="website_sale.search">
                  <t t-set="search" t-value="original_search or search" />
                  <t t-set="_form_classes" t-valuef="d-lg-inline {{'d-inline' if not category else 'd-none'}}" />
                </t> -->

                <t t-call="website_sale.pricelist_list" t-cache="pricelist">
                  <t t-set="_classes" t-valuef="d-none d-lg-inline" />
                </t>

                <!-- <t t-if="is_view_active('website_sale.sort')" t-call="website_sale.sort">
                  <t t-set="_classes" t-valuef="d-none d-lg-inline-block" />
                </t> -->

                <div
                  t-if="category"
                  class="d-flex align-items-center d-lg-none me-auto"
                >
                  <t
                    t-if="not category.parent_id"
                    t-set="backUrl"
                    t-valuef="/shop"
                  />
                  <t
                    t-else=""
                    t-set="backUrl"
                    t-value="keep('/shop/category/' + slug(category.parent_id), category=0)"
                  />

                  <a
                    t-attf-class="btn btn-{{ navClass }} me-2"
                    t-att-href="category.parent_id and keep('/shop/category/' + slug(category.parent_id), category=0) or '/shop'"
                  >
                    <i class="fa fa-angle-left" />
                  </a>
                  <h4 t-out="category.name" class="mb-0 me-auto" />
                </div>

                <!-- <t t-if="is_view_active('website_sale.add_grid_or_list_option')" t-call="website_sale.add_grid_or_list_option">
                  <t t-set="_classes" t-valuef="d-flex" />
                </t>

                <button t-if="is_view_active('website_sale.sort') or opt_wsale_categories or opt_wsale_attributes or opt_wsale_attributes_top" t-attf-class="btn btn-{{
                    navClass
                  }} position-relative {{not opt_wsale_attributes_top and 'd-lg-none'}}" data-bs-toggle="offcanvas" data-bs-target="#o_wsale_offcanvas">
                  <i class="fa fa-sliders" />
                  <span t-if="isFilteringByPrice or attrib_set or tags" t-attf-class="position-absolute top-0 start-100 translate-middle border border-{{
                      navClass
                    }} rounded-circle bg-danger p-1"><span class="visually-hidden">filters active</span></span
                  >
                </button> -->
              </div>

              <div
                t-if="original_search and products"
                class="alert alert-warning mt8"
              >
                No results found for '<span t-esc="original_search" /> '.
                Showing results for '<span t-esc="search" />
                '.
              </div>

              <t t-if="category">
                <t t-set="editor_msg"
                  >Drag building blocks here to customize the header for "<t
                    t-esc="category.name"
                  />
                  " category.</t
                >
                <div
                  class="mb16"
                  id="category_header"
                  t-att-data-editor-message="editor_msg"
                  t-field="category.website_description"
                />
              </t>

              <div t-if="products" class="row">
                <t t-foreach="bins" t-as="tr_product">
                  <t t-foreach="tr_product" t-as="td_product">
                    <t t-if="td_product">
                      <article
                        class="pbmit-ele-blog pbmit-blog-style-1 col-md-6 col-lg-3"
                      >
                        <div class="post-item">
                          <div class="pbminfotech-box-content">
                            <t t-call="website_sale.products_item">
                              <t
                                t-set="product"
                                t-value="td_product['product']"
                              />
                            </t>
                          </div>
                        </div>
                      </article>
                    </t>
                  </t>
                </t>
              </div>

              <div
                t-else=""
                t-nocache="get the actual search"
                class="text-center text-muted mt128 mb256"
              >
                <t t-if="not search">
                  <h3 class="mt8">No product defined</h3>
                  <p t-if="category">No product defined in this category.</p>
                </t>
                <t t-else="">
                  <h3 class="mt8">No results</h3>
                  <p>
                    No results for "<strong t-esc="search" />
                    "
                    <t t-if="category">
                      in category "<strong t-esc="category.display_name" /> "</t
                    >.
                  </p>
                </t>
                <p t-ignore="true" groups="sales_team.group_sale_manager">
                  Click <i>'New'</i> in the top-right corner to create your
                  first product.
                </p>
              </div>

              <div
                class="products_pager d-flex justify-content-center pt-5 pb-3"
              >
                <t t-call="website.pager" />
              </div>
            </div>
          </div>

          <t t-call="website_sale.o_wsale_offcanvas" />
        </div>
        <div
          class="oe_structure oe_empty oe_structure_not_nearest"
          id="oe_structure_website_sale_products_2"
        />
      </div>
    </xpath>
  </template>
  <!-- WEB SALE PRODUCTS -->

  <!-- RELATED TO THE WEB SALE PRODUCTS -->
  <template
    id="products_attributes"
    inherit_id="theme_kdmobilier.custom_products"
    active="True"
    name="Attributes &amp; Variants filters"
  >
    <xpath
      expr="//div[hasclass('products_attributes_filters')]"
      position="inside"
    >
      <t t-if="category and not category.child_id">
        <div id="wsale_products_attributes_collapse" class="position-relative">
          <form
            t-if="attributes or all_tags"
            class="js_attributes position-relative mb-2"
            method="get"
          >
            <input
              t-if="category"
              type="hidden"
              name="category"
              t-att-value="category.id"
            />
            <input type="hidden" name="search" t-att-value="search" />
            <input type="hidden" name="order" t-att-value="order" />
            <a
              t-if="attrib_values or tags"
              t-att-href="keep('/shop'+ ('/category/'+slug(category)) if category else None, attrib=0, tags=0)"
              t-attf-class="btn btn-{{
                navClass
              }} d-flex align-items-center py-1 mb-2"
            >
              <small class="mx-auto">
                <b>Clear Filters</b>
              </small>
              <i class="oi oi-close" />
            </a>
            <t t-foreach="attributes" t-as="a">
              <t t-cache="a,attrib_set">
                <div
                  class="accordion-item nav-item mb-1 border-0"
                  t-if="a.value_ids and len(a.value_ids) &gt; 1"
                >
                  <h6 class="mb-3">
                    <b
                      class="o_products_attributes_title d-none d-lg-block"
                      t-field="a.name"
                    />
                  </h6>
                  <div t-attf-id="o_products_attributes_{{ a.id }}" class="">
                    <t t-if="a.display_type == 'select'">
                      <select
                        class="form-select css_attribute_select mb-2"
                        name="attrib"
                      >
                        <option value="" selected="true">-</option>
                        <t t-foreach="a.value_ids" t-as="v">
                          <option
                            t-att-value="'%s-%s' % (a.id,v.id)"
                            t-esc="v.name"
                            t-att-selected="v.id in attrib_set"
                          />
                        </t>
                      </select>
                    </t>
                    <div t-elif="a.display_type == 'color'" class="mb-3">
                      <t
                        t-call="website_sale.o_wsale_offcanvas_color_attribute"
                      />
                    </div>
                    <div
                      t-elif="a.display_type in ('radio', 'pills', 'multi')"
                      class="flex-column mb-3"
                    >
                      <t t-foreach="a.value_ids" t-as="v">
                        <div class="form-check mb-1">
                          <input
                            type="checkbox"
                            name="attrib"
                            class="form-check-input"
                            t-att-id="'%s-%s' % (a.id,v.id)"
                            t-att-value="'%s-%s' % (a.id,v.id)"
                            t-att-checked="'checked' if v.id in attrib_set else None"
                          />
                          <label
                            class="form-check-label fw-normal"
                            t-att-for="'%s-%s' % (a.id,v.id)"
                            t-field="v.name"
                          />
                        </div>
                      </t>
                    </div>
                  </div>
                </div>
              </t>
            </t>
            <t
              t-if="opt_wsale_filter_tags and opt_wsale_attributes"
              t-call="website_sale.filter_products_tags"
            >
              <t t-set="all_tags" t-value="all_tags" />
            </t>
          </form>
        </div>
      </t>
    </xpath>
  </template>
  <!-- RELATED TO THE WEB SALE PRODUCTS -->

  <!-- PRODUCTS BREADCRUMB TOP LINKS ON THE CATEGORY -->
  <template
    id="custom_products_breadcrumb"
    name="Custom Products Breadcrumb"
    inherit_id="website_sale.products_breadcrumb"
  >
    <xpath
      expr="//ol[@t-attf-class='breadcrumb #{_classes}']"
      position="replace"
    >
      <ol t-if="category" t-attf-class="breadcrumb #{_classes}">
        <li class="breadcrumb-item">
          <a class="fw-normal" href="/shop">Products</a>
        </li>
        <t t-foreach="category.parents_and_self" t-as="cat">
          <li t-if="cat == category" class="breadcrumb-item">
            <span class="d-inline-block" t-field="cat.name" />
          </li>
          <li t-else="" class="breadcrumb-item">
            <a
              class="fw-normal"
              t-att-href="keep('/shop/category/%s' % slug(cat), category=0)"
              t-field="cat.name"
            />
          </li>
        </t>
      </ol>
    </xpath>
  </template>
  <!-- PRODUCTS BREADCRUMB TOP LINKS ON THE CATEGORY -->

  <!-- PRODUCT ITEM CUSTOM -->
  <template
    id="products_item_blog_style"
    inherit_id="website_sale.products_item"
    name="Product Item Blog Style"
  >
    <xpath expr="//form" position="replace">
      <form
        action="/shop/cart/update"
        method="post"
        class="oe_product_cart h-100 d-flex pbminfotech-box-content"
        t-att-data-publish="product.website_published and 'on' or 'off'"
        itemscope="itemscope"
        itemtype="http://schema.org/Product"
      >
        <t
          t-set="product_href"
          t-value="keep(product.website_url, page=(pager['page']['num'] if pager['page']['num']&gt;1 else None))"
        />
        <t
          t-set="image_type"
          t-value="product._get_suitable_image_size(ppr, td_product['x'], td_product['y'])"
        />

        <div
          class="oe_product_image position-relative h-100 flex-grow-0 overflow-hidden pbmit-featured-container"
        >
          <input
            type="hidden"
            name="csrf_token"
            t-att-value="request.csrf_token()"
            t-nocache="The csrf token must always be up to date."
          />
          <a
            t-att-href="product_href"
            class="oe_product_image_link d-block h-100 position-relative pbmit-featured-img-wrapper"
            itemprop="url"
            contenteditable="false"
          >
            <t t-set="image_holder" t-value="product._get_image_holder()" />
            <span
              t-field="image_holder.image_1920"
              t-options="{'widget': 'image', 'preview_image': image_type, 'itemprop': 'image', 'class': 'h-100 w-100 position-absolute'}"
              class="oe_product_image_img_wrapper d-flex h-100 justify-content-center align-items-center position-absolute pbmit-featured-wrapper"
            />

            <t t-if="product.product_template_image_ids">
              <span
                t-field="product.product_template_image_ids[0].image_1920"
                t-options="{'widget': 'image', 'preview_image': 'image_1024', 'class': 'h-100 w-100 position-absolute hover-product-image'}"
                class="oe_product_image_img_wrapper d-flex h-100 justify-content-center align-items-center position-absolute pbmit-featured-wrapper"
              />
            </t>

            <a class="pbmit-link" t-att-href="product_href"></a>
            <t
              t-set="bg_color"
              t-value="td_product['ribbon']['bg_color'] or ''"
            />
            <t
              t-set="text_color"
              t-value="td_product['ribbon']['text_color']"
            />
            <t t-set="bg_class" t-value="td_product['ribbon']['html_class']" />
            <span
              t-attf-class="o_ribbon o_not_editable #{bg_class}"
              t-attf-style="#{text_color and ('color: %s; ' % text_color)}#{bg_color and 'background-color:' + bg_color}"
              t-out="td_product['ribbon']['html'] or ''"
            />
          </a>
        </div>
        <div
          class="o_wsale_product_information position-relative d-flex flex-column flex-grow-1 flex-shrink-1"
        >
          <div class="o_wsale_product_information_text flex-grow-1">
            <h6 class="o_wsale_products_item_title mb-2">
              <a
                class="text-primary text-decoration-none"
                itemprop="name"
                t-att-href="product_href"
                t-att-content="product.name"
                t-field="product.name"
              />
              <a
                t-if="not product.website_published"
                role="button"
                t-att-href="product_href"
                class="btn btn-sm btn-danger"
                title="This product is unpublished."
              >
                Unpublished
              </a>
            </h6>
          </div>
          <div
            class="o_wsale_product_sub d-flex justify-content-between align-items-end gap-2 flex-wrap pb-1"
          >
            <t
              t-set="template_price_vals"
              t-value="get_product_prices(product)"
            />
            <div class="o_wsale_product_btn" />
            <!-- <div class="product_price" itemprop="offers" itemscope="itemscope" itemtype="http://schema.org/Offer">
          <t t-if="'base_price' in template_price_vals and (template_price_vals['base_price'] &gt; template_price_vals['price_reduce']) and (template_price_vals['price_reduce'] or not website.prevent_zero_price_sale)">
            <del t-attf-class="text-muted me-1 h6 mb-0" style="white-space: nowrap">
              <em class="small" t-esc="template_price_vals['base_price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" />
            </del>
          </t>
          <span class="h6 mb-0 text-primary" t-if="template_price_vals['price_reduce'] or not website.prevent_zero_price_sale" t-esc="template_price_vals['price_reduce']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" />
          <span class="h6 mb-0" t-elif="any(ptav.price_extra for ptav in product.attribute_line_ids.product_template_value_ids)">&amp;nbsp;</span>
          <span class="h6 mb-0 text-primary" t-else="" t-field="website.prevent_zero_price_sale_text" />
          <span class="h6 mb-0 text-primary" t-else="" t-out="'Sur commande'" />
          <span itemprop="price" style="display: none" t-esc="template_price_vals['price_reduce']" />
          <span itemprop="priceCurrency" style="display: none" t-esc="website.currency_id.name" />
        </div> -->
            <div
              class="product_price"
              itemprop="offers"
              itemscope="itemscope"
              itemtype="http://schema.org/Offer"
            >
              <t
                t-if="'base_price' in template_price_vals and (template_price_vals['base_price'] &gt; template_price_vals['price_reduce']) and (template_price_vals['price_reduce'] or not website.prevent_zero_price_sale)"
              >
                <del
                  t-attf-class="text-muted me-1 h6 mb-0"
                  style="white-space: nowrap"
                >
                  <!-- <em class="small" t-out="'Sur commande'" /> -->
                </del>
              </t>
              <!-- <span class="h6 mb-0 text-primary" t-if="template_price_vals['price_reduce'] or not website.prevent_zero_price_sale" t-out="'Sur commande'" /> -->
              <span
                class="h6 mb-0"
                t-elif="any(ptav.price_extra for ptav in product.attribute_line_ids.product_template_value_ids)"
                >&amp;nbsp;</span
              >
              <!-- <span class="h6 mb-0 text-primary" t-else="" t-field="website.prevent_zero_price_sale_text" /> -->

              <span
                itemprop="price"
                style="display: none"
                t-esc="template_price_vals['price_reduce']"
              />
              <span
                itemprop="priceCurrency"
                style="display: none"
                t-esc="website.currency_id.name"
              />
            </div>
          </div>
        </div>
      </form>
    </xpath>
  </template>
  <!-- PRODUCT ITEM CUSTOM -->

  <!-- TOP NAV CUSTOM CATEGORIES  -->
  <!-- <template id="custom_filmstrip_categories" name="Custom Filmstrip Categories" inherit_id="website_sale.filmstrip_categories">
<xpath expr="//div[@class='o_wsale_filmstip_container d-flex align-items-stretch mb-2 overflow-hidden']" position="replace">
  <div t-if="entries" class="o_wsale_filmstip_container d-flex align-items-stretch overflow-hidden" style="min-height: 120px">
    <div class="o_wsale_filmstip_wrapper overflow-auto">
      <ul class="o_wsale_filmstip d-flex align-items-stretch p-0 mb-0 list-unstyled overflow-visible" style="gap: 1rem">
        <t t-foreach="entries" t-as="c" t-if="c.image_128" t-set="atLeastOneImage" t-value="True" />
        <t t-if="category.parent_id" t-set="backUrl" t-value="keep('/shop/category/' + slug(category.parent_id), category=0)" />
        <t t-else="" t-set="backUrl" t-value="'/shop'" />

        <li t-foreach="entries" t-as="c" t-attf-class="d-flex {{'pe-4' if not c_last else ''}}" t-att-data-link-href="keep('/shop/category/' + slug(c), category=0)">
          <input type="radio" t-attf-name="wsale_categories_top_radios_{{ parentCategoryId }}" class="btn-check pe-none" t-att-id="c.id" t-att-value="c.id" t-att-checked="'true' if c.id == category.id else None" />
          <div t-attf-class="btn btn-{{
                  navClass
                }} d-flex align-items-center {{'ps-3 pe-4' if c.image_128 else 'px-5'}} fs-5 fw-normal {{ 'border border-2 border-primary' if c.id == category.id else '' }}" t-att-for="c.id" style="min-height: 80px">
            <div t-if="c.image_128" t-attf-style="
                background-image:url('data:image/png;base64,#{c.image_128}');
                width: 64px;
                height: 64px; 
              " class="o_image_64_cover oe_img_bg o_bg_img_center rounded-3 me-3" t-att-alt="c.name" />
            <span t-field="c.name" class="fs-5" />

          </div>
        </li>
      </ul>
    </div>
  </div>
</xpath>
</template> -->
  <!-- TOP NAV CUSTOM CATEGORIES  -->

  <!-- PRODUCT PAGE CUSTOM -->
  <template
    id="custom_product"
    name="Custom Product"
    inherit_id="website_sale.product"
  >
    <xpath expr="//div[@id='wrap']" position="replace">
      <div
        itemscope="itemscope"
        itemtype="http://schema.org/Product"
        id="wrap"
        t-attf-class="js_sale o_wsale_product_page #{request.website.viewref('website_sale.product_picture_magnify_hover').active and 'ecom-zoomable' or ''} #{request.website.viewref('website_sale.product_picture_magnify_hover').active and 'zoomodoo-next' or ''} #{request.website.viewref('website_sale.product_picture_magnify_click').active and 'ecom-zoomable' or ''} #{request.website.viewref('website_sale.product_picture_magnify_both').active and 'ecom-zoomable zoomodoo-next' or ''}"
        t-att-data-ecom-zoom-auto="(request.website.viewref('website_sale.product_picture_magnify_hover').active or request.website.viewref('website_sale.product_picture_magnify_both').active) and '1' or None"
        t-att-data-ecom-zoom-click="(request.website.viewref('website_sale.product_picture_magnify_click').active or request.website.viewref('website_sale.product_picture_magnify_both').active) and '1' or None"
      >
        <div
          class="oe_structure oe_empty oe_structure_not_nearest"
          id="oe_structure_website_sale_product_1"
          data-editor-message="DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
        />
        <section
          id="product_detail"
          t-attf-class="container py-4 oe_website_sale #{'discount' if combination_info['has_discounted_price'] else ''}"
          t-att-data-view-track="view_track and '1' or '0'"
          t-att-data-product-tracking-info="'product_tracking_info' in combination_info and json.dumps(combination_info['product_tracking_info'])"
        >
          <div class="row align-items-center">
            <!-- <div class="col-lg-6 d-flex align-items-center">
              <div class="d-flex justify-content-between w-100">
                <t t-if="is_view_active('website_sale.search')" t-call="website_sale.search">
                  <t t-set="search" t-value="False" />
                  <t t-set="_form_classes" t-valuef="mb-2 mb-lg-0" />
                  <t t-set="_classes" t-value="'me-sm-2'" />
                </t>
                <t t-set="website_sale_pricelists" t-value="website.get_pricelist_available(show_visible=True)" />
                <t t-set="hasPricelistDropdown" t-value="website_sale_pricelists and len(website_sale_pricelists)&gt;1" />
                <t t-call="website_sale.pricelist_list">
                  <t t-set="_classes" t-valuef="d-lg-inline ms-2" />
                </t>
              </div>
            </div> -->
            <div class="col-lg-6 d-flex align-items-center">
              <ol class="breadcrumb p-0 mb-2 m-lg-0">
                <li class="breadcrumb-item o_not_editable">
                  <a t-att-href="keep(category=0)">All Products</a>
                </li>
                <li
                  t-nocache="The category does not have to be cached, as the product can be accessed via different paths."
                  t-if="category"
                  class="breadcrumb-item"
                >
                  <a
                    t-att-href="keep('/shop/category/%s' % slug(category), category=0)"
                    t-field="category.name"
                  />
                </li>
                <li class="breadcrumb-item active">
                  <span t-field="product.name" />
                </li>
              </ol>
            </div>
          </div>
          <div
            class="row"
            id="product_detail_main"
            data-name="Product Page"
            t-att-data-image_width="website.product_page_image_width"
            t-att-data-image_layout="website.product_page_image_layout"
          >
            <t
              t-set="image_cols"
              t-value="website._get_product_page_proportions()"
            />
            <div
              t-attf-class="col-lg-#{image_cols[0]} mt-lg-4 o_wsale_product_images position-relative"
              t-if="website.product_page_image_width != 'none'"
            >
              <t t-call="website_sale.shop_product_images" />
            </div>
            <div
              t-attf-class="col-lg-#{image_cols[1]} mt-md-4"
              id="product_details"
            >
              <t t-set="base_url" t-value="product.get_base_url()" />
              <h1 itemprop="name" t-field="product.name">Product Name</h1>
              <span
                itemprop="url"
                style="display: none"
                t-esc="base_url + product.website_url"
              />
              <span
                itemprop="image"
                style="display: none"
                t-esc="base_url + website.image_url(product, 'image_1920')"
              />
              <t t-if="is_view_active('website_sale.product_comment')">
                <a
                  href="#o_product_page_reviews"
                  class="o_product_page_reviews_link text-decoration-none"
                >
                  <t t-call="portal_rating.rating_widget_stars_static">
                    <t t-set="rating_avg" t-value="product.rating_avg" />
                    <t t-set="trans_text_plural">%s reviews</t>
                    <t t-set="trans_text_singular">%s review</t>
                    <t
                      t-set="rating_count"
                      t-value="(trans_text_plural if product.rating_count > 1 else trans_text_singular) % product.rating_count"
                    />
                  </t>
                </a>
              </t>
              <p
                t-field="product.description_sale"
                class="text-muted my-2"
                placeholder="A short description that will also appear on documents."
              />
              <div
                t-field="product.description_ecommerce"
                class="oe_structure"
                placeholder="A detailed, formatted description to promote your product on this page. Use '/' to discover more features."
              />
              <form
                t-if="product._is_add_to_cart_possible()"
                action="/shop/cart/update"
                method="POST"
              >
                <input
                  type="hidden"
                  name="csrf_token"
                  t-att-value="request.csrf_token()"
                  t-nocache="The csrf token must always be up to date."
                />
                <div class="js_product js_main_product mb-3">
                  <div>
                    <t t-call="website_sale.product_price" />
                    <small
                      t-if="combination_info['base_unit_price']"
                      class="ms-1 text-muted o_base_unit_price_wrapper d-none"
                      groups="website_sale.group_show_uom_price"
                    >
                      <t t-call="website_sale.base_unit_price" />
                    </small>
                  </div>
                  <t t-placeholder="select">
                    <input
                      type="hidden"
                      class="product_id"
                      name="product_id"
                      t-att-value="product_variant.id"
                    />
                    <input
                      type="hidden"
                      class="product_template_id"
                      name="product_template_id"
                      t-att-value="product.id"
                    />
                    <input
                      t-if="product.public_categ_ids.ids"
                      type="hidden"
                      class="product_category_id"
                      name="product_category_id"
                      t-att-value="product.public_categ_ids.ids[0]"
                    />
                    <t t-call="theme_kdmobilier.custom_variants">
                      <t t-set="ul_class" t-valuef="flex-column" />
                      <t t-set="parent_combination" t-value="None" />
                    </t>
                  </t>
                  <p
                    t-if="True"
                    class="css_not_available_msg alert alert-warning"
                  >
                    This combination does not exist.
                  </p>
                  <div
                    id="o_wsale_cta_wrapper"
                    class="d-flex flex-wrap align-items-center"
                  >
                    <t t-set="hasQuantities" t-value="false" />
                    <t t-set="hasBuyNow" t-value="false" />
                    <t
                      t-set="ctaSizeBig"
                      t-value="not hasQuantities or not hasBuyNow"
                    />
                    <strong
                      class="attribute_name w-100 bg-light mb-3"
                      style="height: 1px"
                    />
                    <!-- AJOUTER AU PANIER FONCTIONNEL -->
                    <!-- <div id="add_to_cart_wrap" t-attf-class="{{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} align-items-center mb-2 me-4">
                  <a data-animation-selector=".o_wsale_product_images" role="button" id="add_to_cart" t-attf-class="btn btn-primary js_check_product a-submit flex-grow-1" href="#">
                    <i class="fa fa-shopping-cart me-2" />
                        Acheter
                  </a>
                </div> -->
                    <!-- AJOUTER AU PANIER FONCTIONNEL FIN -->
                    <div
                      id="add_to_cart_wrap"
                      t-attf-class="{{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} align-items-center mb-2 me-auto"
                    >
                      <a
                        t-attf-class="btn btn-success flex-grow-1"
                        t-att-href="'https://api.whatsapp.com/send?phone=221785332828&amp;text=Je+suis+int%C3%A9ress%C3%A9+par+ce+produit%3A+' + product.name + '%0ALien+du+produit+dans+le+site+de+KD+MOBILIER%3A+' + base_url + product.website_url"
                        target="_blank"
                      >
                        <i class="fa fa-whatsapp me-2" />
                        COMMANDER
                      </a>
                    </div>

                    <div
                      id="product_option_block"
                      class="d-flex flex-wrap w-100"
                    />

                    <!-- <h6 class="text-primary h6">PARTAGEZ CE PRODUIT</h6> -->
                    <!-- <div id="o_product_terms_and_share" class="d-flex justify-content-between flex-column flex-md-row-reverse align-items-md-start mb-3"></div> -->
                  </div>

                  <div
                    id="contact_us_wrapper"
                    t-attf-class="{{'d-flex' if combination_info['prevent_zero_price_sale'] else 'd-none'}} oe_structure oe_structure_solo #{_div_classes}"
                  >
                    <section
                      class="s_text_block"
                      data-snippet="s_text_block"
                      data-name="Text"
                    >
                      <div class="container">
                        <a
                          t-att-href="website.contact_us_button_url"
                          class="btn btn-primary btn_cta"
                          >Contact Us
                        </a>
                      </div>
                    </section>
                  </div>
                  <t
                    t-if="is_view_active('website_sale.product_tags')"
                    t-call="website_sale.product_tags"
                  >
                    <t
                      t-set="all_product_tags"
                      t-value="product_variant.all_product_tag_ids"
                    />
                  </t>
                </div>
              </form>
              <p t-elif="not product.active" class="alert alert-warning">
                This product is no longer available.
              </p>
              <p t-else="" class="alert alert-warning">
                This product has no valid combination.
              </p>
              <div id="product_attributes_simple">
                <t
                  t-set="single_value_attributes"
                  t-value="product.valid_product_template_attribute_line_ids._prepare_single_value_for_display()"
                />
                <table
                  t-attf-class="table table-sm text-muted {{'' if single_value_attributes else 'd-none'}}"
                >
                  <t t-foreach="single_value_attributes" t-as="attribute">
                    <tr>
                      <td>
                        <span t-field="attribute.name" />
                        :
                        <t
                          t-foreach="single_value_attributes[attribute]"
                          t-as="ptal"
                        >
                          <span
                            t-field="ptal.product_template_value_ids._only_active().name"
                          />
                          <t t-if="not ptal_last">, </t>
                        </t>
                      </td>
                    </tr>
                  </t>
                </table>
              </div>
              <t
                t-set="product_documents"
                t-value="product.sudo().product_document_ids.filtered(lambda doc: doc.shown_on_product_page)"
              />
              <div id="product_documents" class="my-2" t-if="product_documents">
                <h5>Documents</h5>
                <t t-foreach="product_documents" t-as="document_sudo">
                  <t
                    t-set="attachment_sudo"
                    t-value="document_sudo.ir_attachment_id"
                  />
                  <t
                    t-set="target"
                    t-value="attachment_sudo.type == 'url' and '_blank' or '_self'"
                  />
                  <t
                    t-set="icon"
                    t-value="attachment_sudo.type == 'url' and 'fa-link' or 'fa-download'"
                  />
                  <div>
                    <a
                      t-att-href="'/shop/' + slug(product) + '/document/' + str(document_sudo.id)"
                      t-att-target="target"
                    >
                      <i t-att-class="'fa ' + icon" />
                      <t t-out="attachment_sudo.name" />
                    </a>
                  </div>
                </t>
              </div>
              <div
                id="o_product_terms_and_share"
                class="d-flex justify-content-between flex-column flex-md-row align-items-md-end mb-3"
              ></div>

              <div>
                <h6 class="text-primary h6">PLUS D'INFORMATION</h6>
                <a
                  title="Pour avoir plus d'information sur ce produit, cliquez ici."
                  t-attf-class="flex-grow-1 text-succcess"
                  t-att-href="'https://api.whatsapp.com/send?phone=221785332828&amp;text=Je+suis+int%C3%A9ress%C3%A9+par+ce+produit%3A+' + product.name + '%0ALien+du+produit+dans+le+site+de+KD+MOBILIER%3A+' + base_url + product.website_url"
                  target="_blank"
                >
                  <i class="fa fa-2x fa-whatsapp text-success me-2"></i>
                </a>
              </div>
            </div>
          </div>
        </section>
        <div
          itemprop="description"
          t-field="product.website_description"
          class="oe_structure oe_empty mt16"
          id="product_full_description"
        />
        <div
          class="oe_structure oe_empty oe_structure_not_nearest mt16"
          id="oe_structure_website_sale_product_2"
          data-editor-message="DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
        />
      </div>
    </xpath>
  </template>

  <!-- PRODUCT PAGE CUSTOM GENERAL CONDITONS AND SHARE-->

  <!-- PRODUCT QUATITY INSIDE OF PRODUCT PAGE CUSTOM NEXT TO ADD TO CART -->

  <template
    id="product_quantity"
    inherit_id="theme_kdmobilier.custom_product"
    name="Select Quantity"
  >
    <xpath expr="//t[@t-set='hasQuantities']" position="attributes">
      <attribute name="t-value" remove="false" add="true" separator=" " />
    </xpath>
    <xpath expr="//div[@id='add_to_cart_wrap']" position="before">
      <div
        t-if="product_variant.detailed_type != 'service'"
        t-attf-class="css_quantity input-group {{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} me-2 mb-2 align-middle"
        contenteditable="false"
      >
        <a
          t-attf-href="#"
          class="btn btn-link js_add_cart_json"
          aria-label="Remove one"
          title="Remove one"
        >
          <i class="fa fa-minus"></i>
        </a>
        <input
          type="text"
          class="form-control quantity text-center"
          data-min="1"
          name="add_qty"
          t-att-value="add_qty or 1"
        />
        <a
          t-attf-href="#"
          class="btn btn-link float_left js_add_cart_json"
          aria-label="Add one"
          title="Add one"
        >
          <i class="fa fa-plus"></i>
        </a>
      </div>
    </xpath>
  </template>

  <!-- PRODUCT QUATITY INSIDE OF PRODUCT PAGE CUSTOM NEXT TO ADD TO CART -->
  <template
    id="custom_product_share_buttons"
    inherit_id="website_sale.product_share_buttons"
    active="True"
    name="Share Buttons"
    priority="22"
  >
    <xpath
      expr="//div[@class='h4 mt-3 mb-0 d-flex justify-content-md-end flex-shrink-0']"
      position="replace"
    >
      <div
        class="h4 mt-3 mb-0 d-flex justify-content-md-end flex-shrink-0"
        contenteditable="false"
      >
        <t t-snippet-call="website.s_share">
          <t
            t-set="_exclude_share_links"
            t-value="['linkedin', 'pinterest', 'email', ]"
          />
          <t t-set="_no_title" t-value="True" />
          <t t-set="_classes" t-valuef="text-lg-end" />
          <t t-set="_link_classes" t-valuef="mx-1 my-0" />
        </t>
      </div>
    </xpath>
  </template>

  <!-- CONDITIONS GÉNÉRALES -->
  <template
    inherit_id="website_sale.product_custom_text"
    customize_show="True"
    active="True"
    id="product_custom_text"
    name="thTerms and Conditions"
    priority="21"
  >
    <xpath expr="//p[@class='text-muted mb-0']" position="replace">
      <p class="text-muted mb-0">
        <a href="/terms" class="text-muted">
          <u>Conditions générales</u>
        </a>
        <br />
        <!-- 30-day money-back guarantee<br /> -->
        <!-- Expédition : 45 jours ouvrables -->
        Expédition: Plus tot possible
      </p>
    </xpath>
  </template>
  <!-- CONDITIONS GÉNÉRALES -->

  <!-- WEBSITE SALE MAGNIFIC POPUP -->

  <!-- Remove navbar pills -->
  <template
    id="remove_navbar_pills"
    inherit_id="website.navbar_nav"
    name="Remove Navbar Pills"
    priority="99"
  >
    <xpath expr="//ul[@id='top_menu']" position="attributes">
      <attribute name="t-attf-class" remove="nav-pills" separator=" " />
    </xpath>
  </template>
  <!-- Header 1 KD mobilier -->

  <!-- Called in `website_sale.reduction_code`. -->
  <template
    id="custom_coupon"
    inherit_id="website_sale.coupon_form"
    name="Coupon form"
  >
    <!-- Checkout context:
          - redirect: The route to redirect to when a customer enters a coupon; default: `None`.
          - website_sale_order: The current order.
      -->
    <xpath expr="//form[@name='coupon_code']" position="replace">
      <form
        t-attf-action="/shop/pricelist#{redirect and '?r=' + redirect or ''}"
        method="post"
        name="coupon_code"
      >
        <input
          type="hidden"
          name="csrf_token"
          t-att-value="request.csrf_token()"
          t-nocache="The csrf token must always be up to date."
        />
        <div class="input-group w-100 my-2">
          <input
            name="promo"
            class="form-control"
            type="text"
            placeholder="Code de remise..."
            t-att-value="website_sale_order.pricelist_id.code or None"
          />
          <a href="#" role="button" class="btn btn-secondary py-3 a-submit ps-2"
            >Appliquer</a
          >
        </div>
      </form>
    </xpath>
  </template>

  <!-- WEBSITE SALE ADDRESS -->

  <template id="address_inherited" inherit_id="website_sale.address">
    <!-- Add contact-form-area class -->
    <!-- <xpath expr="//div[@class='oe_cart col-12 col-lg-8']" position="attributes">
      <attribute name="class" add="contact-form-area" separator=" " />
    </xpath> -->

    <!-- Modify country select t-attf-class -->
    <!-- <xpath expr="//select[@id='country_id']" position="attributes">
  <attribute name="t-attf-class">form-select form-control #{error.get('country_id') and 'is-invalid' or
        ''}</attribute
      >
</xpath> -->

    <!-- Modify state select t-attf-class -->
    <!-- <xpath expr="//select[@name='state_id']" position="attributes">
  <attribute name="t-attf-class">form-select form-control #{error.get('state_id') and 'is-invalid' or
        ''}</attribute
      >
</xpath> -->

    <!-- Remove field_required attribute -->
    <!-- <xpath expr="//input[@name='field_required']" position="attributes">
  <attribute name="value">name,email,street,city,country_id</attribute>
</xpath> -->
  </template>

  <!-- WEBSITE SALE ADDRESS -->

  <!-- <template id="template_kdmobilier_header_1" inherit_id="website.layout" name="Template KD mobilier Header 1">
    </template> -->

  <!-- VARIANTS STARTS -->
  <template id="custom_variants" inherit_id="website_sale.variants">
    <xpath expr="//ul" position="replace">
      <ul
        t-attf-class="list-unstyled js_add_cart_variants mb-0 #{ul_class}"
        t-att-data-attribute_exclusions="json.dumps(attribute_exclusions)"
      >
        <t
          t-foreach="product.valid_product_template_attribute_line_ids"
          t-as="ptal"
        >
          <!-- Attributes selection is hidden if there is only one value available and it's not a custom value -->
          <li
            t-att-data-attribute_id="ptal.attribute_id.id"
            t-att-data-attribute_name="ptal.attribute_id.name"
            t-att-data-attribute_display_type="ptal.attribute_id.display_type"
            t-attf-class="variant_attribute #{
                        'd-none' if len(ptal.product_template_value_ids._only_active()) == 1
                        and not ptal.product_template_value_ids._only_active()[0].is_custom
                        and not ptal.attribute_id.display_type == 'multi' else ''
                    }"
          >
            <!-- Used to customize layout if the only available attribute value is custom -->
            <t
              t-set="single"
              t-value="len(ptal.product_template_value_ids._only_active()) == 1"
            />
            <t
              t-set="single_and_custom"
              t-value="single and ptal.product_template_value_ids._only_active()[0].is_custom"
            />

            <t
              t-if="ptal.attribute_id.name == 'Couleur' and 'Canape' in product.name"
            >
              <div class="row">
                <div class="col-5">
                  <strong class="attribute_name">Type de cuirs</strong>
                </div>
                <div class="col-7">
                  <strong
                    t-field="ptal.attribute_id.name"
                    class="attribute_name"
                  />
                </div>
              </div>
            </t>
            <t t-else="">
              <strong t-field="ptal.attribute_id.name" class="attribute_name" />
            </t>

            <!-- SELECTED DISPLAY TYPE IS SELECT -->
            <!-- <t t-if="ptal.attribute_id.display_type == 'select'">
              <select t-att-data-attribute_id="ptal.attribute_id.id" t-attf-class="form-select css_attribute_select o_wsale_product_attribute js_variant_change #{ptal.attribute_id.create_variant} #{'d-none' if single_and_custom else ''}" t-attf-name="ptal-#{ptal.id}">
                <t t-foreach="ptal.product_template_value_ids._only_active()" t-as="ptav">
                  <option t-att-value="ptav.id" t-att-data-value_id="ptav.id" t-att-data-value_name="ptav.name" t-att-data-attribute_name="ptav.attribute_id.name" t-att-data-is_custom="ptav.is_custom" t-att-selected="ptav in combination" t-att-data-is_single="single" t-att-data-is_single_and_custom="single_and_custom">
                    <span t-field="ptav.name" />
                    <t t-call="website_sale.badge_extra_price" />
                  </option>
                </t>
              </select>
            </t> -->
            <t t-if="ptal.attribute_id.display_type == 'select'">
              <ul
                t-att-data-attribute_id="ptal.attribute_id.id"
                t-attf-class="btn-group-toggle list-inline product-set-products list-unstyled o_wsale_product_attribute #{'d-none' if single_and_custom else ''}"
                data-bs-toggle="buttons"
              >
                <t
                  t-foreach="ptal.product_template_value_ids._only_active()"
                  t-as="ptav"
                >
                  <li
                    t-attf-class="o_variant_pills canape col-12 d-flex btn btn-primary h-auto mb-1 list-inline-item js_attribute_value #{'active' if ptav in combination else ''}"
                    style="text-align: inherit"
                  >
                    <input
                      type="radio"
                      t-attf-class="js_variant_change #{ptal.attribute_id.create_variant}"
                      t-att-checked="ptav in combination"
                      t-attf-name="ptal-#{ptal.id}"
                      t-att-value="ptav.id"
                      t-att-data-value_id="ptav.id"
                      t-att-id="ptav.id"
                      t-att-data-value_name="ptav.name"
                      t-att-data-attribute_name="ptav.attribute_id.name"
                      t-att-data-is_custom="ptav.is_custom"
                      t-att-data-is_single_and_custom="single_and_custom"
                      t-att-autocomplete="off"
                    />
                    <label
                      class="radio_input_value w-100 o_variant_pills_input_value row align-items-center"
                      t-att-for="ptav.id"
                    >
                      <div class="col-8 col-md-7 col-lg-6 text-left">
                        <span class="productname" t-field="ptav.name" />
                        <t t-call="website_sale.badge_extra_price" />
                      </div>
                      <div
                        class="col-4 col-md-3 col-lg-4 text-right offset-md-2"
                      >
                        <span class="text-right">
                          <!-- <img class="img-fluid" src="/theme_kdmobilier/static/src/images/bamboo.jpeg" /> -->
                        </span>
                      </div>
                    </label>
                  </li>
                </t>
              </ul>
            </t>

            <!-- SELECTED DISPLAY TYPE IS SELECT -->
            <t t-elif="ptal.attribute_id.display_type in ('radio', 'multi')">
              <ul
                t-att-data-attribute_id="ptal.attribute_id.id"
                t-attf-class="list-inline list-unstyled o_wsale_product_attribute #{'d-none' if single_and_custom else ''}"
              >
                <t
                  t-foreach="ptal.product_template_value_ids._only_active()"
                  t-as="ptav"
                >
                  <li
                    class="list-inline-item mb-3 js_attribute_value"
                    style="margin: 0"
                  >
                    <label class="col-form-label">
                      <div class="form-check">
                        <input
                          t-att-type="'radio' if ptal.attribute_id.display_type == 'radio' else 'checkbox'"
                          t-attf-class="form-check-input js_variant_change #{ptal.attribute_id.create_variant}"
                          t-att-checked="ptav in combination"
                          t-attf-name="ptal-#{ptal.id}"
                          t-att-value="ptav.id"
                          t-att-data-value_id="ptav.id"
                          t-att-data-value_name="ptav.name"
                          t-att-data-attribute_name="ptav.attribute_id.name"
                          t-att-data-is_custom="ptav.is_custom"
                          t-att-data-is_single="single if ptal.attribute_id.display_type != 'multi' else False"
                          t-att-data-is_single_and_custom="single_and_custom"
                        />
                        <div class="radio_input_value form-check-label">
                          <span t-field="ptav.name" />
                          <t t-call="website_sale.badge_extra_price" />
                        </div>
                      </div>
                    </label>
                  </li>
                </t>
              </ul>
            </t>

            <t t-elif="ptal.attribute_id.display_type == 'pills'">
              <ul
                t-att-data-attribute_id="ptal.attribute_id.id"
                t-attf-class="btn-group-toggle list-inline list-unstyled o_wsale_product_attribute #{'d-none' if single_and_custom else ''}"
                data-bs-toggle="buttons"
              >
                <t
                  t-foreach="ptal.product_template_value_ids._only_active()"
                  t-as="ptav"
                >
                  <li
                    t-attf-class="o_variant_pills btn btn-primary mb-1 list-inline-item js_attribute_value #{'active' if ptav in combination else ''}"
                  >
                    <input
                      type="radio"
                      t-attf-class="js_variant_change #{ptal.attribute_id.create_variant}"
                      t-att-checked="ptav in combination"
                      t-attf-name="ptal-#{ptal.id}"
                      t-att-value="ptav.id"
                      t-att-data-value_id="ptav.id"
                      t-att-id="ptav.id"
                      t-att-data-value_name="ptav.name"
                      t-att-data-attribute_name="ptav.attribute_id.name"
                      t-att-data-is_custom="ptav.is_custom"
                      t-att-data-is_single_and_custom="single_and_custom"
                      t-att-autocomplete="off"
                    />
                    <label
                      class="radio_input_value o_variant_pills_input_value"
                      t-att-for="ptav.id"
                    >
                      <span t-field="ptav.name" />
                      <t t-call="website_sale.badge_extra_price" />
                    </label>
                  </li>
                </t>
              </ul>
            </t>

            <!-- <t t-elif="ptal.attribute_id.display_type == 'color'">
              <ul t-att-data-attribute_id="ptal.attribute_id.id" t-attf-class="list-inline o_wsale_product_attribute #{'d-none' if single_and_custom else ''}">
                <li t-foreach="ptal.product_template_value_ids._only_active()" t-as="ptav" class="list-inline-item me-1">
                  <span t-field="ptav.name" />
                  <t t-set="img_style" t-value="ptav.image and ('background:url(/web/image/product.template.attribute.value/' + str(ptav.id) + '/image); background-size:cover;') or ''" />
                  <t t-set="color_style" t-value="ptav.html_color and ('background:' + ptav.html_color) or ''" />
                  <label t-attf-style="#{img_style or color_style}" t-attf-class="css_attribute_color #{'active' if ptav in combination else ''} #{'custom_value' if ptav.is_custom else ''} #{'transparent' if (not ptav.is_custom and not ptav.html_color) else ''}">
                    <input type="radio" t-attf-class="js_variant_change  #{ptal.attribute_id.create_variant}" t-att-checked="ptav in combination" t-attf-name="ptal-#{ptal.id}" t-att-value="ptav.id" t-att-title="ptav.name" t-att-data-value_id="ptav.id" t-att-data-value_name="ptav.name" t-att-data-attribute_name="ptav.attribute_id.name" t-att-data-is_custom="ptav.is_custom" t-att-data-is_single="single" t-att-data-is_single_and_custom="single_and_custom" />
                  </label>
                </li>
              </ul>
            </t> -->

            <t t-elif="ptal.attribute_id.display_type == 'color'">
              <div class="leather-collections">
                <!-- Art Royal - Cuir Lisse -->
                <div class="leather-collection row mb-2">
                  <div class="col-12 col-md-5 d-flex align-items-center">
                    <div class="d-flex align-items-center">
                      <h6
                        class="h6 mb-0 mr-2 collection-title d-flex align-items-center"
                      >
                        <span>Cuir Lisse</span>
                        <a
                          href="#"
                          class="leather-type-info ms-2"
                          role="button"
                        >
                          <i class="fa fa-info-circle text-primary"></i>
                        </a>
                      </h6>
                      <span class="color"></span>
                    </div>
                  </div>
                  <ul
                    t-att-data-attribute_id="ptal.attribute_id.id"
                    t-attf-class="list-inline col-12 col-md-7 o_wsale_product_attribute #{'d-none' if single_and_custom else ''}"
                  >
                    <li
                      t-foreach="ptal.product_template_value_ids._only_active().filtered(lambda x: x.name.startswith('Art Royal'))"
                      t-as="ptav"
                      class="list-inline-item me-2 mb-2"
                    >
                      <span t-field="ptav.name" class="d-none" />
                      <t
                        t-set="img_style"
                        t-value="ptav.image and ('background:url(/web/image/product.template.attribute.value/' + str(ptav.id) + '/image); background-size:cover;') or ''"
                      />
                      <t
                        t-set="color_style"
                        t-value="ptav.html_color and ('background:' + ptav.html_color) or ''"
                      />
                      <div
                        class="color-item d-flex flex-column align-items-center"
                      >
                        <label
                          t-attf-style="#{img_style or color_style}"
                          t-attf-class="css_attribute_color rounded-circle #{'active' if ptav in combination else ''} #{'custom_value' if ptav.is_custom else ''} #{'transparent' if (not ptav.is_custom and not ptav.html_color) else ''}"
                          t-att-title="ptav.name"
                        >
                          <input
                            type="radio"
                            t-attf-class="js_variant_change #{ptal.attribute_id.create_variant}"
                            t-att-checked="ptav in combination"
                            t-attf-name="ptal-#{ptal.id}"
                            t-att-value="ptav.id"
                            t-att-title="ptav.name"
                            t-att-data-value_id="ptav.id"
                            t-att-data-value_name="ptav.name"
                            t-att-data-attribute_name="ptav.attribute_id.name"
                            t-att-data-is_custom="ptav.is_custom"
                            t-att-data-is_single="single"
                            t-att-data-is_single_and_custom="single_and_custom"
                          />
                        </label>
                      </div>
                    </li>
                  </ul>
                </div>

                <!-- Dollaro - Cuir Grainé -->
                <div class="leather-collection row mb-2">
                  <div class="col-12 col-md-5 d-flex align-items-center">
                    <div class="d-flex align-items-center">
                      <h6
                        class="h6 mb-0 mr-2 collection-title d-flex align-items-center"
                      >
                        <span>Cuir Grainé</span>
                        <a
                          href="#"
                          class="leather-type-info ms-2"
                          role="button"
                        >
                          <i class="fa fa-info-circle text-primary"></i>
                        </a>
                      </h6>
                      <span class="color"></span>
                    </div>
                  </div>
                  <ul
                    t-att-data-attribute_id="ptal.attribute_id.id"
                    t-attf-class="list-inline col-12 col-md-7 o_wsale_product_attribute #{'d-none' if single_and_custom else ''}"
                  >
                    <li
                      t-foreach="ptal.product_template_value_ids._only_active().filtered(lambda x: x.name.startswith('Dollaro'))"
                      t-as="ptav"
                      class="list-inline-item me-2 mb-2"
                    >
                      <span t-field="ptav.name" class="d-none" />
                      <t
                        t-set="img_style"
                        t-value="ptav.image and ('background:url(/web/image/product.template.attribute.value/' + str(ptav.id) + '/image); background-size:cover;') or ''"
                      />
                      <t
                        t-set="color_style"
                        t-value="ptav.html_color and ('background:' + ptav.html_color) or ''"
                      />
                      <div
                        class="color-item d-flex flex-column align-items-center"
                      >
                        <label
                          t-attf-style="#{img_style or color_style}"
                          t-attf-class="css_attribute_color rounded-circle #{'active' if ptav in combination else ''} #{'custom_value' if ptav.is_custom else ''} #{'transparent' if (not ptav.is_custom and not ptav.html_color) else ''}"
                          t-att-title="ptav.name"
                        >
                          <input
                            type="radio"
                            t-attf-class="js_variant_change #{ptal.attribute_id.create_variant}"
                            t-att-checked="ptav in combination"
                            t-attf-name="ptal-#{ptal.id}"
                            t-att-value="ptav.id"
                            t-att-title="ptav.name"
                            t-att-data-value_id="ptav.id"
                            t-att-data-value_name="ptav.name"
                            t-att-data-attribute_name="ptav.attribute_id.name"
                            t-att-data-is_custom="ptav.is_custom"
                            t-att-data-is_single="single"
                            t-att-data-is_single_and_custom="single_and_custom"
                          />
                        </label>
                      </div>
                    </li>
                  </ul>
                </div>

                <!-- Statuto - Cuir Texturé -->
                <!-- Statuto - Cuir Texturé -->
              </div>
            </t>
          </li>
        </t>
      </ul>
    </xpath>
  </template>
  <!-- VARIANTS ENDS -->

  <!-- HELP DESK TEMPLATE CUSTOM -->
  <!-- Inherit from the base helpdesk team template -->
  <template
    id="custom_team_template"
    inherit_id="website_helpdesk.team"
    name="Custom Helpdesk Team Template"
  >
    <xpath expr="//div[hasclass('container')]" position="replace">
      <section class="pbmit-sticky-section">
        <div class="container">
          <div class="contact-us-bg">
            <div class="row">
              <!-- Left Column - Info -->
              <div class="col-md-12 col-xl-4">
                <div class="pbmit-sticky-col">
                  <div class="contact-us-left-area">
                    <div class="pbmit-heading-subheading animation-style2">
                      <h4 class="pbmit-subtitle">Centre d'Aide</h4>
                      <h2 class="pbmit-title">Besoin d'Assistance ?</h2>
                      <div class="pbmit-heading-desc">
                        Soumettez votre ticket et notre équipe de support vous
                        répondra dans les plus brefs délais.
                      </div>
                    </div>
                    <div
                      t-if="not is_html_empty(team.description)"
                      class="mt-4"
                    >
                      <div class="card bg-secondary mt-2" id="about_team">
                        <h6 class="card-header">
                          <b>À propos de notre équipe</b>
                        </h6>
                        <div class="card-body">
                          <span t-field="team.description" />
                        </div>
                      </div>
                    </div>
                    <div
                      class="oe_structure"
                      id="oe_structure_website_helpdesk_team_2"
                    />
                    <div
                      class="oe_structure"
                      id="oe_structure_website_helpdesk_team_3"
                    />
                  </div>
                </div>
              </div>

              <!-- Right Column - Form -->
              <div class="col-md-12 col-xl-8">
                <div class="contact-form-area">
                  <div
                    class="oe_structure w-100 mb-2"
                    id="oe_structure_website_helpdesk_team_1"
                  />
                  <div t-if="team.use_website_helpdesk_form">
                    <t
                      t-set="template_xmlid"
                      t-value="team.website_form_view_id.xml_id"
                    />
                    <t t-call="#{template_xmlid}" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="oe_structure" id="oe_structure_website_helpdesk_team_4" />
      </section>
    </xpath>
  </template>

  <!-- HELP DESK TEMPLATE CUSTOM END -->

  <!-- WISHLIST CUSTOM BUTTON -->

  <template
    id="add_to_wishlist_custom"
    inherit_id="website_sale_wishlist.add_to_wishlist"
    name="Wishlist Button"
    priority="20"
  >
    <xpath expr="//button[hasclass('o_add_wishlist')]" position="replace">
      <button
        t-if="product_variant_id"
        type="button"
        role="button"
        class="btn btn-outline-primary bg-white o_add_wishlist"
        t-att-disabled="in_wish or None"
        title="Ajouter à la liste de souhaits"
        t-att-data-product-template-id="product.id"
        t-att-data-product-product-id="product_variant_id"
        data-action="o_wishlist"
      >
        <span
          t-attf-class="fa {{ 'fa-heart' if in_wish else 'fa-heart-o' }}"
          role="img"
          aria-label="Ajouter à la liste de souhaits"
        />
      </button>
    </xpath>
  </template>
  <!-- WISHLIST CUSTOM BUTTON -->
  <!-- WISHLIST PAGE CUSTOM -->
  <template
    id="wishlist_page_custom"
    inherit_id="website_sale_wishlist.product_wishlist"
    name="Wishlist Page"
  >
    <xpath
      expr="//table[@class='table table-bordered table-striped table-hover text-center mt16 table-comparator ']"
      position="replace"
    >
      <table
        class="table table-bordered table-striped table-hover text-center mt16 table-comparator"
        style="table-layout: auto"
        id="o_comparelist_table"
      >
        <body>
          <t t-foreach="wishes" t-as="wish">
            <t
              t-set="combination_info"
              t-value="wish.product_id._get_combination_info_variant()"
            />
            <tr
              t-att-data-wish-id="wish.id"
              t-att-data-product-id="wish.product_id.id"
              t-att-data-product-tracking-info="'product_tracking_info' in combination_info and json.dumps(combination_info['product_tracking_info'])"
            >
              <td class="td-img align-middle d-none d-md-block">
                <a t-att-href="wish.product_id.website_url">
                  <img
                    t-attf-src="/web/image/product.product/#{wish.product_id.id}/image_128"
                    class="img img-fluid"
                    style="margin: auto"
                    alt="Product image"
                  />
                </a>
              </td>
              <td class="text-start align-middle">
                <strong>
                  <a t-att-href="wish.product_id.website_url">
                    <t t-esc="wish.product_id.display_name" />
                  </a>
                </strong>
                <small class="d-none d-md-block">
                  <p
                    t-field="wish.product_id.description_sale"
                    class="text-muted"
                  />
                </small>
                <button
                  type="button"
                  class="btn btn-link o_wish_rm no-decoration"
                >
                  <small> <i class="fa fa-trash-o"></i> Remove</small>
                </button>
              </td>
              <td
                class="align-middle"
                t-if="combination_info['prevent_zero_price_sale']"
              >
                <span t-field="website.prevent_zero_price_sale_text" />
              </td>
              <td class="align-middle o_wish_price" t-else="">
                <!-- <t t-out="combination_info['price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" /> -->
                <small
                  t-if="combination_info['base_unit_price']"
                  class="cart_product_base_unit_price d-block text-muted"
                  groups="website_sale.group_show_uom_price"
                >
                  <t t-call="website_sale.base_unit_price">
                    <!-- <t t-set="product" t-value="wish.product_id" /> -->
                  </t>
                </small>
              </td>
              <!-- <td class="text-center td-wish-btn align-middle">
            <input name="product_id" t-att-value="wish.product_id.id" type="hidden" />
            <a t-if="combination_info['prevent_zero_price_sale']" t-att-href="website.contact_us_button_url" class="btn btn-primary btn_cta">Contact Us</a
                >
            <button id="add_to_cart_button" t-else="" type="button" role="button" class="btn btn-secondary btn-block o_wish_add mb4">
                  Add <span class="d-none d-md-inline">to Cart</span>
            </button>
          </td> -->
            </tr>
          </t>
        </body>
      </table>
    </xpath>
  </template>
  <!-- WISHLIST PAGE CUSTOM END -->

  <!-- SERVIECE SIDEBAR TEMPLATE -->
  <!-- Sidebar Template 1 -->
  <template id="services_sidebar">
    <aside class="service-sidebar">
      <!-- Navigation des services -->
      <aside class="widget post-list">
        <h2 class="widget-title">Nos Services</h2>
        <div class="all-post-list">
          <ul>
            <li>
              <a
                t-attf-href="/services/design-architecture"
                t-attf-class="#{request.httprequest.path == '/services/design-architecture' and 'active' or ''}"
              >
                Design et Architecture d'intérieur
              </a>
            </li>
            <li>
              <a
                t-attf-href="/services/entretien-canape"
                t-attf-class="#{request.httprequest.path == '/services/entretien-canape' and 'active' or ''}"
              >
                Entretien de Canapé
              </a>
            </li>
            <li>
              <a
                t-attf-href="/services/conseil-decoration"
                t-attf-class="#{request.httprequest.path == '/services/conseil-decoration' and 'active' or ''}"
              >
                Conseil en Décoration
              </a>
            </li>
          </ul>
        </div>
      </aside>

      <!-- Widget Newsletter -->
      <!-- <aside class="widget pbmit-service-ad">
        <div class="textwidget">
          <div class="pbmit-service-ads">
            <h5 class="pbmit-ads-subheding">Newsletter KD Mobilier</h5>
            <h4 class="pbmit-ads-subtitle">
              Prêt à transformer votre intérieur ?
            </h4>
            <h3 class="pbmit-ads-title">Inscrivez-vous!</h3>
            <div class="pbmit-ads-desc">
              <i class="pbmit-base-icon-phone-call-1"></i>
              +221 78 533 28 28
            </div>
            <a class="pbmit-btn pbmit-btn-hover-white" href="/newsletter">
              <span class="pbmit-button-content-wrapper">
                <span class="pbmit-button-text">S'inscrire maintenant</span>
              </span>
            </a>
          </div>
        </div>
      </aside> -->

      <!-- Documents utiles -->
      <!-- <aside class="widget pbmit-download-content">
        <h2 class="widget-title">Documents utiles</h2>
        <div class="textwidget">
          <div class="download">
            <div class="item-download">
              <a href="/catalogue" target="_blank" rel="noopener noreferrer">
                <span class="pbmit-download-content">
                  <i class="pbmit-base-icon-pdf-file-format-symbol-1"></i>
                  Catalogue 2024
                </span>
                <span class="pbmit-download-item">
                  <i class="pbminfotech-base-icons pbmit-righticon pbmit-base-icon-download"></i>
                </span>
              </a>
            </div>
            <div class="item-download">
              <a href="/guide" target="_blank" rel="noopener noreferrer">
                <span class="pbmit-download-content">
                  <i class="pbmit-base-icon-pdf-file-format-symbol-1"></i>
                  Guide des mesures
                </span>
                <span class="pbmit-download-item">
                  <i class="pbminfotech-base-icons pbmit-righticon pbmit-base-icon-download"></i>
                </span>
              </a>
            </div>
          </div>
        </div>
      </aside> -->

      <!-- Widget Contact CTA -->
      <aside class="widget pbmit-contact-cta">
        <div class="textwidget">
          <div class="pbmit-cta-box">
            <h4>Besoin d'aide ?</h4>
            <p>Nos experts sont à votre disposition</p>
            <a href="/contactus" class="pbmit-btn">
              <span class="pbmit-button-content-wrapper">
                <span class="pbmit-button-text">Contactez-nous</span>
              </span>
            </a>
          </div>
        </div>
      </aside>
    </aside>
  </template>

  <!-- Sidebar Template 2 -->
  <template id="services_sidebar_1">
    <aside class="widget post-list">
      <h2 class="widget-title">Nos Services</h2>
      <div class="all-post-list">
        <ul>
          <li>
            <a href="/services/design-architecture"
              >Design et Architecture d'intérieur</a
            >
          </li>
          <li>
            <a href="/services/entretien-canape">Entretien de Canapé</a>
          </li>
          <li>
            <a href="/services/conseil-decoration">Conseil en Décoration</a>
          </li>
        </ul>
      </div>
    </aside>

    <aside class="widget pbmit-service-ad">
      <div class="textwidget">
        <div class="pbmit-service-ads">
          <h5 class="pbmit-ads-subheding">Contactez-nous</h5>
          <div class="pbmit-ads-desc">
            <i class="pbmit-base-icon-phone-call-1"></i>
            +221 78 533 28 28
          </div>
          <a class="pbmit-btn pbmit-btn-hover-white" href="/contactus">
            Prendre rendez-vous
          </a>
        </div>
      </div>
    </aside>
  </template>

  <!-- SERVIECE SIDEBAR TEMPLATE -->
  <!-- WEBSITE SALE SUBSCRIPTION -->
  <template
    id="hide_subscription_products_item"
    inherit_id="website_sale_subscription.products_item"
  >
    <xpath expr="//span[@class='o_subscription_unit']" position="attributes">
      <attribute name="t-if">False</attribute>
    </xpath>
  </template>

  <template
    id="hide_subscription_product_price"
    inherit_id="website_sale_subscription.subscription_product_price"
  >
    <xpath expr="//span[@class='o_subscription_unit']" position="attributes">
      <attribute name="t-if">False</attribute>
    </xpath>
  </template>
  <!-- WEBSITE SALE SUBSCRIPTION -->

  <!-- WEBSITE CHECKOUT STYLING CHANGE -->

  <template
    id="custom_checkout_layout"
    inherit_id="website_sale.checkout_layout"
    name="Checkout layout page"
  >
    <xpath
      expr="//div[@class='o_cta_navigation_container position-absolute position-lg-static start-0 bottom-0 col-12']"
      position="replace"
    >
      <div
        t-if="show_navigation_button"
        class="o_cta_navigation_container position-static start-0 bottom-0 col-12"
      >
        <t t-call="website_sale.navigation_buttons" />
      </div>
    </xpath>
  </template>

  <!-- WEBSITE CHECKOUT STYLING CHANGE -->

  <!-- CUSTOM PRODUCT PRICE -->

  <template
    id="custom_product_price"
    inherit_id="website_sale.product_price"
    name="Custom Product Price"
  >
    <xpath expr="//div[@id='product_unavailable']" position="replace">
      <div
        id="product_unavailable"
        t-attf-class="{{'d-flex' if combination_info['prevent_zero_price_sale'] else 'd-none'}}"
      >
        <!-- <h3 class="fst-italic" t-out="'Sur commande'" /> -->
      </div>
    </xpath>
  </template>
  <!-- CUSTOM PRODUCT PRICE END -->

  <!-- CUSTOM BADGE EXTRA PRICE -->
  <template
    id="custom_badge_extra_price"
    name="Custom Badge Extra Price"
    inherit_id="website_sale.badge_extra_price"
  >
    <xpath
      expr="//span[@class='badge rounded-pill text-bg-light border']"
      position="replace"
    >
      <!-- LEAVE IT EMPTY TO REMOVE THE BADGE EXTRA PRICE -->
    </xpath>
  </template>
  <!-- CUSTOM BADGE EXTRA PRICE END  -->

  <!-- CUSTOM PRODUCT PRICE -->
  <template
    id="custom_product_price_1"
    inherit_id="website_sale.product_price"
    name="Custom Product Price"
  >
    <xpath expr="//div[@itemscope='itemscope']" position="replace">
      <div
        itemprop="offers"
        itemscope="itemscope"
        itemtype="http://schema.org/Offer"
        t-attf-class="product_price mt-2 mb-3 {{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-block'}}"
      >
        <h3 class="css_editable_mode_hidden">
          <!-- Sur commande -->
          <!-- <span class="oe_price" style="white-space: nowrap" t-out="combination_info['price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" />
          <span itemprop="price" style="display: none" t-out="combination_info['price']" />
          <span itemprop="priceCurrency" style="display: none" t-esc="website.currency_id.name" />
          <span t-attf-class="text-danger oe_default_price ms-1 h5 {{'' if combination_info['has_discounted_price'] and not combination_info['compare_list_price'] else 'd-none'}}" style="text-decoration: line-through; white-space: nowrap" t-esc="combination_info['list_price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" itemprop="listPrice" />
          <t t-if="is_view_active('website_sale.tax_indication')" t-call="website_sale.tax_indication" />
          <del t-if="combination_info['compare_list_price'] and (combination_info['compare_list_price'] &gt; combination_info['price'])">
            <bdi dir="inherit">
              <span t-esc="combination_info['compare_list_price']" groups="website_sale.group_product_price_comparison" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}" />
            </bdi>
          </del> -->
        </h3>
        <h3
          class="css_non_editable_mode_hidden decimal_precision"
          t-att-data-precision="str(website.currency_id.decimal_places)"
        >
          hello 1
          <span
            t-field="product.list_price"
            t-options="{'widget': 'monetary', 'display_currency': product.currency_id}"
          />
          <t
            t-if="is_view_active('website_sale.tax_indication')"
            t-call="website_sale.tax_indication"
          />
          <del
            t-if="combination_info['compare_list_price'] and (combination_info['compare_list_price'] &gt; combination_info['price'])"
          >
            <bdi dir="inherit">
              <span
                t-field="product.compare_list_price"
                groups="website_sale.group_product_price_comparison"
                t-options="{'widget': 'monetary', 'display_currency': product.currency_id}"
              />
            </bdi>
          </del>
        </h3>
      </div>
    </xpath>
  </template>
  <!-- CUSTOM PRODUCT PRICE END -->

  <!-- USER SIGN IN WITH ICON -->
  <template
    id="user_sign_in"
    name="User Sign In"
    inherit_id="portal.user_sign_in"
  >
    <xpath expr="//li" position="replace">
      <li
        t-nocache="Profile session and user group can change unrelated to parent caches."
        t-nocache-_item_class="_item_class"
        t-nocache-_link_class="_link_class"
        groups="base.group_public"
        t-attf-class="#{_item_class} o_no_autohide_item"
      >
        <a t-attf-href="/web/login" t-attf-class="#{_link_class}">
          <span
            t-if="request.session.profile_session"
            class="text-danger fa fa-circle"
          />
          <i class="fa fa-user" aria-label="Sign in"></i>
        </a>
      </li>
    </xpath>
  </template>
  <!-- USER SIGN IN WITH ICON END -->
</odoo>
