<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <template id="kdmobilier_homepage" inherit_id="website.homepage" name="Homepage">
    <xpath expr="//t[@t-call='website.layout']" position="replace">
      <t t-call="website.layout">
        <t t-set="pageName" t-value="'homepage'" />

        <div class="oe_structure oe_empty" />
        <div class="all-content">
          <!-- Hero Video Section -->
          <section class="video-section-alt">
            <div class="video-container">
              <video autoplay="true" loop="true" muted="true" playsinline="true">
                <source src="https://res.cloudinary.com/do4eujx4o/video/upload/q_auto,f_auto/v1743361363/bwwmouqs3w1kbzvwzefz.mp4" type="video/mp4" />
              </video>
            </div>
            <div class="video-overlay" style="
                background: linear-gradient(
                  to bottom,
                  rgba(0, 0, 0, 0.1) 0%,
                  rgba(0, 0, 0, 0.2) 50%,
                  rgba(0, 0, 0, 0.4) 100%
                );
              ">            </div>
            <div class="corner-content">
              <div class="left-text">
                <h2 class="corner-title" style="
                    font-family: var(--pbmit-heading-typography-font-family);
                    font-weight: 300;
                    letter-spacing: 2px;
                  ">
                  COLLECTION DE MOBILIER HAUT DE GAMME
                </h2>
                <p class="corner-description" style="font-weight: 300; letter-spacing: 1px">
                  Un design moderne et élégant pour un intérieur raffiné
                </p>
              </div>
              <a href="/shop" class="corner-link" style="
                  background-color: var(--pbmit-global-color);
                  padding: 12px 30px;
                  border-radius: 30px;
                  letter-spacing: 1.5px;
                  font-size: 0.9rem;
                  transition: all 0.3s ease;
                ">
                <span>Voir les produits</span>
              </a>
            </div>
          </section>

          <!-- Console Banner -->
          <div class="position-relative overflow-hidden" style="margin-top: 2rem; margin-bottom: 2rem">
            <img src="theme_kdmobilier/static/src/images/4938-b-modo-tv.webp" alt="Console Design" class="img-fluid w-100" style="object-fit: cover; height: auto; max-height: 80vh" loading="lazy" />
            <div class="position-absolute" style="
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(
                  to bottom,
                  rgba(0, 0, 0, 0.1) 0%,
                  rgba(0, 0, 0, 0.2) 50%,
                  rgba(0, 0, 0, 0.4) 100%
                );
              ">            </div>
            <div class="position-absolute" style="
                bottom: 50px;
                width: 100%;
                padding: 0 50px;
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                z-index: 2;
              ">
              <div style="max-width: 400px">
                <h2 style="
                    color: #fff;
                    font-size: 1.8rem;
                    font-weight: 300;
                    margin-bottom: 1rem;
                    letter-spacing: 2px;
                    font-family: var(--pbmit-heading-typography-font-family);
                  ">
                  CONSOLES &amp; MEUBLES TV
                </h2>
                <p style="
                    color: #fff;
                    font-size: 1rem;
                    font-weight: 300;
                    line-height: 1.6;
                    letter-spacing: 0.5px;
                  ">
                  Une élégance intemporelle pour votre espace de vie
                </p>
              </div>
              <a href="/shop/category/consoles-tv-22" class="corner-link" style="
                  background-color: var(--pbmit-global-color);
                  padding: 12px 30px;
                  border-radius: 30px;
                  color: #fff;
                  text-decoration: none;
                  letter-spacing: 1.5px;
                  font-size: 0.9rem;
                  transition: all 0.3s ease;
                ">
                <span>DÉCOUVRIR</span>
              </a>
            </div>
          </div>

          <!-- Art De Vivre Video Section -->
          <section class="saloni-video-section">
            <div class="saloni-video-container">
              <video autoplay="true" loop="true" muted="true" playsinline="true">
                <source src="https://res.cloudinary.com/do4eujx4o/video/upload/q_auto,f_auto/v1743361469/zovllz7juaegd4xful5l.mp4" type="video/mp4" />
              </video>
            </div>
            <div class="saloni-video-overlay" style="
                background: linear-gradient(
                  to bottom,
                  rgba(0, 0, 0, 0.1) 0%,
                  rgba(0, 0, 0, 0.2) 50%,
                  rgba(0, 0, 0, 0.4) 100%
                );
              ">            </div>
            <div class="saloni-video-text">
              <h2 class="saloni-video-title" style="
                  font-family: var(--pbmit-heading-typography-font-family);
                  font-weight: 200;
                ">
                L'ART DE VIVRE
              </h2>
              <p class="saloni-video-description" style="font-weight: 300; margin-bottom: 2rem">
                Découvrez notre collection exclusive, où le design moderne
                rencontre le confort intemporel. Chaque pièce est
                méticuleusement conçue pour créer un espace de vie qui reflète
                votre style unique.
              </p>
              <a href="/shop" class="corner-link" style="
                  background-color: var(--pbmit-global-color);
                  padding: 12px 30px;
                  border-radius: 30px;
                  color: #fff;
                  text-decoration: none;
                  letter-spacing: 1.5px;
                  font-size: 0.9rem;
                  transition: all 0.3s ease;
                  display: inline-block;
                  margin-top: 1rem;
                ">
                <span>EXPLORER</span>
              </a>
            </div>
          </section>

          <!-- Bedroom Banner -->
          <div class="position-relative overflow-hidden" style="margin-top: 2rem; margin-bottom: 2rem">
            <img src="/theme_kdmobilier/static/src/images/5141-b-anasayfa-test.webp" alt="Chambre Moderne" class="img-fluid w-100" style="object-fit: cover; height: auto; max-height: 80vh" loading="lazy" />
            <div class="position-absolute" style="
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(
                  to bottom,
                  rgba(0, 0, 0, 0.1) 0%,
                  rgba(0, 0, 0, 0.2) 50%,
                  rgba(0, 0, 0, 0.4) 100%
                );
              ">            </div>
            <div class="position-absolute" style="
                bottom: 50px;
                width: 100%;
                padding: 0 50px;
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                z-index: 2;
              ">
              <div style="max-width: 400px">
                <h2 style="
                    color: #fff;
                    font-size: 1.8rem;
                    font-weight: 300;
                    margin-bottom: 1rem;
                    letter-spacing: 2px;
                    font-family: var(--pbmit-heading-typography-font-family);
                  ">
                  CHAMBRES ÉLÉGANTES
                </h2>
                <p style="
                    color: #fff;
                    font-size: 1rem;
                    font-weight: 300;
                    line-height: 1.6;
                    letter-spacing: 0.5px;
                  ">
                  Créez un sanctuaire de sérénité et de style
                </p>
              </div>
              <a href="/shop/category/chambres-a-coucher-23" class="corner-link" style="
                  background-color: var(--pbmit-global-color);
                  padding: 12px 30px;
                  border-radius: 30px;
                  color: #fff;
                  text-decoration: none;
                  letter-spacing: 1.5px;
                  font-size: 0.9rem;
                  transition: all 0.3s ease;
                ">
                <span>DÉCOUVRIR</span>
              </a>
            </div>
          </div>

          <!-- Second Video Section -->
          <section class="video-section-alt">
            <div class="video-container">
              <video autoplay="true" loop="true" muted="true" playsinline="true">
                <source src="https://res.cloudinary.com/do4eujx4o/video/upload/q_auto,f_auto/v1743361326/ra1zklploim5p5jyyjzu.mp4" type="video/mp4" />
              </video>
            </div>
            <div class="video-overlay" style="
                background: linear-gradient(
                  to bottom,
                  rgba(0, 0, 0, 0.1) 0%,
                  rgba(0, 0, 0, 0.2) 50%,
                  rgba(0, 0, 0, 0.4) 100%
                );
              ">            </div>
            <div class="corner-content">
              <div class="left-text">
                <h2 class="corner-title" style="
                    font-family: var(--pbmit-heading-typography-font-family);
                    font-weight: 300;
                    letter-spacing: 2px;
                  ">
                  DESIGN &amp; ÉLÉGANCE
                </h2>
                <p class="corner-description" style="font-weight: 300; letter-spacing: 1px">
                  Des espaces de vie qui inspirent et impressionnent
                </p>
              </div>
              <a href="/shop" class="corner-link" style="
                  background-color: var(--pbmit-global-color);
                  padding: 12px 30px;
                  border-radius: 30px;
                  letter-spacing: 1.5px;
                  font-size: 0.9rem;
                  transition: all 0.3s ease;
                ">
                <span>EXPLORER</span>
              </a>
            </div>
          </section>

          <!-- Second Bedroom Banner -->
          <div class="position-relative overflow-hidden" style="margin-top: 2rem; margin-bottom: 2rem">
            <img src="/theme_kdmobilier/static/src/images/5146-b-anasayfa-test.webp" alt="Chambre Luxueuse" class="img-fluid w-100" style="object-fit: cover; height: auto; max-height: 80vh" loading="lazy" />
            <div class="position-absolute" style="
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(
                  to bottom,
                  rgba(0, 0, 0, 0.1) 0%,
                  rgba(0, 0, 0, 0.2) 50%,
                  rgba(0, 0, 0, 0.4) 100%
                );
              ">            </div>
            <div class="position-absolute" style="
                bottom: 50px;
                width: 100%;
                padding: 0 50px;
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                z-index: 2;
              ">
              <div style="max-width: 400px">
                <h2 style="
                    color: #fff;
                    font-size: 1.8rem;
                    font-weight: 300;
                    margin-bottom: 1rem;
                    letter-spacing: 2px;
                    font-family: var(--pbmit-heading-typography-font-family);
                  ">
                  LITS CONFORTABLES
                </h2>
                <p style="
                    color: #fff;
                    font-size: 1rem;
                    font-weight: 300;
                    line-height: 1.6;
                    letter-spacing: 0.5px;
                  ">
                  Dormez dans le confort et l'élégance absolue
                </p>
              </div>
              <a href="/shop/category/lits-24" class="corner-link" style="
                  background-color: var(--pbmit-global-color);
                  padding: 12px 30px;
                  border-radius: 30px;
                  color: #fff;
                  text-decoration: none;
                  letter-spacing: 1.5px;
                  font-size: 0.9rem;
                  transition: all 0.3s ease;
                ">
                <span>DÉCOUVRIR</span>
              </a>
            </div>
          </div>

          <!-- Mobile Grid Force Styles - Improved Design -->
          <style>
            /* Hide prices on all screen sizes */
            .luxury-product-price {
              display: none !important;
            }

            @media screen and (max-width: 768px) {
              .luxury-products-grid {
                display: grid !important;
                grid-template-columns: 1fr 1fr !important;
                gap: 10px !important;
                margin-bottom: 30px !important;
                width: 100% !important;
                max-width: 100% !important;
                padding: 0 !important;
                box-sizing: border-box !important;
              }

              .luxury-product-card {
                border-radius: 16px !important;
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
                background: white !important;
                overflow: hidden !important;
                margin: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
                box-sizing: border-box !important;
                transition: transform 0.3s ease, box-shadow 0.3s ease !important;
              }

              .luxury-product-card:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
              }

              .luxury-product-image-container,
              .premium-collection-luxury .luxury-product-image-container,
              .luxury-products-grid .luxury-product-image-container {
                height: 200px !important;
                position: relative !important;
                overflow: hidden !important;
                background: linear-gradient(135deg, #8a5830, #d19d66) !important;
              }

              .luxury-product-image {
                width: 100% !important;
                height: 100% !important;
                object-fit: cover !important;
                transition: transform 0.3s ease !important;
              }

              .luxury-product-card:hover .luxury-product-image {
                transform: scale(1.05) !important;
              }

              .luxury-product-details {
                padding: 12px !important;
                text-align: center !important;
              }

              .luxury-product-category {
                font-size: 0.65rem !important;
                font-weight: 500 !important;
                color: #8a5830 !important;
                text-transform: uppercase !important;
                letter-spacing: 0.5px !important;
                margin-bottom: 6px !important;
              }

              .luxury-product-name {
                font-size: 0.9rem !important;
                font-weight: 600 !important;
                line-height: 1.3 !important;
                margin-bottom: 8px !important;
                color: #2c3e50 !important;
                display: -webkit-box !important;
                -webkit-line-clamp: 2 !important;
                -webkit-box-orient: vertical !important;
                overflow: hidden !important;
              }

              .luxury-product-tagline {
                font-size: 0.6rem !important;
                color: #7f8c8d !important;
                font-style: italic !important;
                margin-top: 4px !important;
              }

              .luxury-product-separator {
                display: none !important;
              }

              .luxury-quality-badge {
                top: 8px !important;
                right: 8px !important;
                padding: 3px 8px !important;
                font-size: 0.55rem !important;
                background: rgba(138, 88, 48, 0.9) !important;
                color: white !important;
                border-radius: 12px !important;
                backdrop-filter: blur(10px) !important;
              }

              .luxury-badge-star {
                margin-right: 2px !important;
              }

              .luxury-product-overlay {
                background: rgba(0, 0, 0, 0.7) !important;
                opacity: 0 !important;
                transition: opacity 0.3s ease !important;
              }

              .luxury-product-card:hover .luxury-product-overlay {
                opacity: 1 !important;
              }

              .luxury-quick-view-btn {
                background: rgba(255, 255, 255, 0.95) !important;
                color: #8a5830 !important;
                padding: 8px 16px !important;
                border-radius: 20px !important;
                font-size: 0.7rem !important;
                font-weight: 600 !important;
                text-transform: uppercase !important;
                letter-spacing: 0.5px !important;
                backdrop-filter: blur(10px) !important;
              }
            }

            @media screen and (max-width: 480px) {
              .luxury-products-grid {
                gap: 8px !important;
              }

              .luxury-product-image-container,
              .premium-collection-luxury .luxury-product-image-container,
              .luxury-products-grid .luxury-product-image-container {
                height: 180px !important;
                background: linear-gradient(135deg, #8a5830, #d19d66) !important;
              }

              .luxury-product-details {
                padding: 10px !important;
              }

              .luxury-product-name {
                font-size: 0.85rem !important;
              }

              .luxury-product-category {
                font-size: 0.6rem !important;
              }
            }
          </style>

          <!-- Products Section -->
          <section class="premium-collection-luxury py-5">
            <!-- Premium decorative elements -->
            <div class="luxury-collection-backdrop"></div>
            <div class="luxury-collection-top-border"></div>
            <div class="luxury-collection-bottom-border"></div>
            <div class="luxury-floating-elements">
              <div class="luxury-element luxury-element-1"></div>
              <div class="luxury-element luxury-element-2"></div>
              <div class="luxury-element luxury-element-3"></div>
            </div>

            <div class="container">
              <div class="luxury-section-header">
                <div class="luxury-section-badge">
                  <span class="luxury-badge-icon">✦</span>
                  <span class="luxury-badge-text">COLLECTION PRESTIGE</span>
                  <span class="luxury-badge-icon">✦</span>
                </div>
                <h2 class="luxury-section-title">
                  <span class="luxury-title-main">CANAPÉS</span>
                  <span class="luxury-title-subtitle">DE LUXE</span>
                  <div class="luxury-title-underline">
                    <div class="luxury-underline-dot"></div>
                  </div>
                </h2>
                <p class="luxury-section-description">
                  Découvrez l'excellence de notre collection exclusive de canapés,
                  où l'artisanat traditionnel rencontre le design contemporain pour créer
                  des pièces d'exception qui transformeront votre espace de vie.
                </p>
              </div>

              <div class="luxury-products-showcase">
                <!-- Fetch Canapés category products only -->

                <!-- Fetch Canapés category products -->
                <t t-set="canape_categories" t-value="request.env['product.public.category'].search([
                  '|', '|', '|', '|', '|',
                  ('name', 'ilike', 'canapé'),
                  ('name', 'ilike', 'canape'),
                  ('name', 'ilike', 'sofa'),
                  ('name', 'ilike', 'salon'),
                  ('name', 'ilike', 'couch'),
                  ('name', 'ilike', 'furniture')
                ])" />

                <!-- Search for products in canapé categories -->
                <t t-set="products" t-value="request.env['product.template'].search([
                  ('website_published', '=', True),
                  ('public_categ_ids', 'in', canape_categories.ids if canape_categories else [])
                ], limit=6)" />

                <!-- Fallback: search for products with canapé in the name -->
                <t t-if="not products">
                  <t t-set="products" t-value="request.env['product.template'].search([
                    ('website_published', '=', True),
                    '|', '|', '|',
                    ('name', 'ilike', 'canapé'),
                    ('name', 'ilike', 'canape'),
                    ('name', 'ilike', 'sofa'),
                    ('name', 'ilike', 'salon')
                  ], limit=6)" />
                </t>

                <!-- Final fallback: show any published products -->
                <t t-if="not products">
                  <t t-set="products" t-value="request.env['product.template'].search([
                    ('website_published', '=', True)
                  ], limit=6)" />
                </t>



                <div class="luxury-products-grid mobile-grid-2">
                  <t t-foreach="products" t-as="product">
                    <div class="luxury-product-card mobile-compact-card">
                      <a t-att-href="product.website_url" class="luxury-product-link">
                        <div class="luxury-product-image-container">
                          <img t-att-src="website.image_url(product, 'image_1024')" t-att-alt="product.name" class="luxury-product-image" />
                          <div class="luxury-product-overlay">
                            <div class="luxury-overlay-content">
                              <div class="luxury-quick-view-btn">
                                <span class="luxury-btn-text">DÉCOUVRIR</span>
                                <span class="luxury-btn-icon">→</span>
                              </div>
                            </div>
                          </div>
                          <!-- Premium quality badge -->
                          <div class="luxury-quality-badge">
                            <span class="luxury-badge-star">★</span>
                            <span class="luxury-badge-label">PREMIUM</span>
                          </div>
                        </div>

                        <div class="luxury-product-details">
                          <t t-if="product.public_categ_ids">
                            <div class="luxury-product-category" t-esc="product.public_categ_ids[0].name" />
                          </t>
                          <h3 class="luxury-product-name" t-esc="product.name" />
                          <div class="luxury-product-separator">
                            <div class="luxury-separator-line"></div>
                            <div class="luxury-separator-diamond">◆</div>
                            <div class="luxury-separator-line"></div>
                          </div>
                          <div class="luxury-product-tagline">Collection Exclusive KDMobilier</div>

                          <!-- Price display if available -->
                          <t t-if="product.list_price">
                            <div class="luxury-product-price">
                              <span class="luxury-price-label">À partir de</span>
                              <span class="luxury-price-amount" t-esc="product.list_price" />
                              <span class="luxury-price-currency">FCFA</span>
                            </div>
                          </t>
                        </div>
                      </a>
                    </div>
                  </t>
                </div>
              </div>

              <!-- Mobile Grid Force JavaScript - Improved -->
              <script>
                <![CDATA[
                (function() {
                  function forceMobileGrid() {
                    if (window.innerWidth <= 768) {
                      const grids = document.querySelectorAll('.luxury-products-grid');
                      grids.forEach(function(grid) {
                        // Grid layout
                        grid.style.setProperty('display', 'grid', 'important');
                        grid.style.setProperty('grid-template-columns', '1fr 1fr', 'important');
                        grid.style.setProperty('gap', '10px', 'important');
                        grid.style.setProperty('margin-bottom', '30px', 'important');
                        grid.style.setProperty('width', '100%', 'important');
                        grid.style.setProperty('max-width', '100%', 'important');
                        grid.style.setProperty('padding', '0', 'important');
                        grid.style.setProperty('box-sizing', 'border-box', 'important');

                        const cards = grid.querySelectorAll('.luxury-product-card');
                        cards.forEach(function(card) {
                          // Card styling
                          card.style.setProperty('border-radius', '16px', 'important');
                          card.style.setProperty('box-shadow', '0 6px 20px rgba(0, 0, 0, 0.12)', 'important');
                          card.style.setProperty('background', 'white', 'important');
                          card.style.setProperty('overflow', 'hidden', 'important');
                          card.style.setProperty('margin', '0', 'important');
                          card.style.setProperty('width', '100%', 'important');
                          card.style.setProperty('max-width', '100%', 'important');
                          card.style.setProperty('box-sizing', 'border-box', 'important');
                          card.style.setProperty('transition', 'transform 0.3s ease, box-shadow 0.3s ease', 'important');

                          // Image container
                          const imageContainer = card.querySelector('.luxury-product-image-container');
                          if (imageContainer) {
                            imageContainer.style.setProperty('height', '160px', 'important');
                            imageContainer.style.setProperty('background', '#f8f9fa', 'important');
                          }

                          // Image styling
                          const image = card.querySelector('.luxury-product-image');
                          if (image) {
                            image.style.setProperty('width', '100%', 'important');
                            image.style.setProperty('height', '100%', 'important');
                            image.style.setProperty('object-fit', 'cover', 'important');
                            image.style.setProperty('transition', 'transform 0.3s ease', 'important');
                          }

                          // Product details
                          const details = card.querySelector('.luxury-product-details');
                          if (details) {
                            details.style.setProperty('padding', '12px', 'important');
                            details.style.setProperty('text-align', 'center', 'important');
                          }

                          // Category styling
                          const category = card.querySelector('.luxury-product-category');
                          if (category) {
                            category.style.setProperty('font-size', '0.65rem', 'important');
                            category.style.setProperty('font-weight', '500', 'important');
                            category.style.setProperty('color', '#8a5830', 'important');
                            category.style.setProperty('text-transform', 'uppercase', 'important');
                            category.style.setProperty('letter-spacing', '0.5px', 'important');
                            category.style.setProperty('margin-bottom', '6px', 'important');
                          }

                          // Product name styling
                          const name = card.querySelector('.luxury-product-name');
                          if (name) {
                            name.style.setProperty('font-size', '0.9rem', 'important');
                            name.style.setProperty('font-weight', '600', 'important');
                            name.style.setProperty('line-height', '1.3', 'important');
                            name.style.setProperty('margin-bottom', '8px', 'important');
                            name.style.setProperty('color', '#2c3e50', 'important');
                            name.style.setProperty('display', '-webkit-box', 'important');
                            name.style.setProperty('-webkit-line-clamp', '2', 'important');
                            name.style.setProperty('-webkit-box-orient', 'vertical', 'important');
                            name.style.setProperty('overflow', 'hidden', 'important');
                          }

                          // Hide elements
                          const separator = card.querySelector('.luxury-product-separator');
                          if (separator) {
                            separator.style.setProperty('display', 'none', 'important');
                          }

                          const priceContainer = card.querySelector('.luxury-product-price');
                          if (priceContainer) {
                            priceContainer.style.setProperty('display', 'none', 'important');
                          }

                          // Quality badge
                          const badge = card.querySelector('.luxury-quality-badge');
                          if (badge) {
                            badge.style.setProperty('top', '8px', 'important');
                            badge.style.setProperty('right', '8px', 'important');
                            badge.style.setProperty('padding', '3px 8px', 'important');
                            badge.style.setProperty('font-size', '0.55rem', 'important');
                            badge.style.setProperty('background', 'rgba(138, 88, 48, 0.9)', 'important');
                            badge.style.setProperty('border-radius', '12px', 'important');
                          }
                        });
                      });
                      console.log('Mobile grid improved design applied to', grids.length, 'grids');
                    }
                  }

                  // Run immediately
                  forceMobileGrid();

                  // Run on resize
                  window.addEventListener('resize', function() {
                    setTimeout(forceMobileGrid, 100);
                  });

                  // Run when DOM is ready
                  if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', forceMobileGrid);
                  }
                })();
                ]]>
              </script>

              <div class="luxury-discover-section">
                <div class="luxury-discover-content">
                  <h3 class="luxury-discover-title">Explorez Notre Collection Complète</h3>
                  <p class="luxury-discover-subtitle">Plus de 50 modèles exclusifs vous attendent</p>
                  <a href="/shop/category/salons-canapes-21" class="luxury-discover-btn">
                    <span class="luxury-btn-content">
                      <span class="luxury-btn-text">VOIR TOUTE LA COLLECTION</span>
                      <span class="luxury-btn-arrow">→</span>
                    </span>
                    <div class="luxury-btn-shine"></div>
                  </a>
                </div>
              </div>
            </div>
          </section>

          <!-- Newsletter Section -->
          <section class="newsletter-section" style="position: relative; background-color: #fff; margin-top: 2rem">
            <div class="newsletter-bg" style="
                background: url('/theme_kdmobilier/static/src/images/5146-b-anasayfa-test.webp')
                  center/cover;
                opacity: 0.1;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
              ">            </div>
            <div class="container">
              <div class="row justify-content-center">
                <div class="col-lg-9 newsletter-content text-center py-5">
                  <h3 class="kdmobilier-section-title" style="
                      font-size: 1.2rem;
                      text-transform: uppercase;
                      letter-spacing: 2px;
                      color: var(--pbmit-global-color);
                      margin-bottom: 1rem;
                    ">
                    NOTRE NEWSLETTER
                  </h3>
                  <div class="gold-line mx-auto" style="
                      width: 60px;
                      height: 3px;
                      background-color: var(--pbmit-global-color);
                      margin-bottom: 2rem;
                    ">                  </div>
                  <h2 class="newsletter-title" style="
                      font-size: 2.5rem;
                      font-weight: 300;
                      margin-bottom: 1.5rem;
                      font-family: var(--pbmit-heading-typography-font-family);
                      letter-spacing: 1px;
                      color: var(--pbmit-heading-color);
                    ">
                    Kdmobilier : Mobilier Haut de Gamme
                  </h2>
                  <p class="newsletter-description" style="
                      font-size: 1.1rem;
                      line-height: 1.8;
                      margin-bottom: 2rem;
                      color: #666;
                      font-weight: 300;
                    ">
                    Rejoignez notre cercle privilégié et découvrez en
                    avant-première nos créations, nos inspirations et nos
                    invitations aux événements exclusifs dédiés à l'art de
                    vivre.
                  </p>

                  <form class="newsletter-form mx-auto" style="max-width: 600px">
                    <div class="row g-3 s_newsletter_subscribe_form s_newsletter_list js_subscribe" data-list-id="1">
                      <div class="col-md-8">
                        <input type="email" name="email" class="form-control js_subscribe_value" placeholder="Votre adresse email" style="
                            height: 50px;
                            border-radius: 25px;
                            border: 1px solid #ddd;
                            padding-left: 20px;
                          " />
                      </div>
                      <div class="col-md-4">
                        <button type="submit" class="submit-btn w-100 js_subscribe_btn" style="
                            height: 50px;
                            border-radius: 25px;
                            background-color: var(--pbmit-global-color);
                            color: white;
                            border: none;
                            font-weight: 500;
                            letter-spacing: 1px;
                            text-transform: uppercase;
                          ">
                          S'inscrire
                        </button>
                        <button type="submit" class="submit-btn w-100 js_subscribed_btn d-none" disabled="disabled" style="
                            height: 50px;
                            border-radius: 25px;
                            background-color: var(--pbmit-global-color);
                            color: white;
                            border: none;
                            font-weight: 500;
                            letter-spacing: 1px;
                          ">
                          Merci
                        </button>
                      </div>
                    </div>
                  </form>

                  <p class="privacy-text" style="margin-top: 1.5rem; font-size: 0.9rem; color: #888">
                    En vous inscrivant, vous acceptez notre
                    <a href="#" style="color: var(--pbmit-global-color)">politique de confidentialité</a
                    >.                    <br />
                    Vous pouvez vous désinscrire à tout moment.
                  </p>
                </div>
              </div>
            </div>
          </section>

          <!-- Services Section - Mobile App Style -->
          <!-- Services Section - Premium Cards Only -->
          <section class="kdm-services-section kdm-premium-cards">
            <div class="container">
              <div class="row justify-content-center">
                <div class="col-lg-10 col-xl-8">
                  <div class="kdm-services-content text-center">
                    <div class="kdm-services-header">
                      <h2 class="kdm-services-title">NOS SERVICES</h2>
                      <div class="kdm-services-divider mx-auto"></div>
                    </div>

                    <!-- Premium Mobile App Style Grid -->
                    <div class="kdm-services-grid kdm-premium-grid">
                      <a href="/services/conseil-decoration" class="kdm-service-card kdm-premium-card">
                        <div class="kdm-service-icon kdm-premium-icon">D</div>
                        <h3 class="kdm-service-title">Conseil Déco</h3>
                        <p class="kdm-service-description">
                          Espaces personnalisés selon votre style
                        </p>
                      </a>

                      <a href="/services/design-architecture" class="kdm-service-card kdm-premium-card">
                        <div class="kdm-service-icon kdm-premium-icon">A</div>
                        <h3 class="kdm-service-title">Architecture</h3>
                        <p class="kdm-service-description">
                          Design élégant et fonctionnel
                        </p>
                      </a>

                      <a href="/services/entretien-canape" class="kdm-service-card kdm-premium-card">
                        <div class="kdm-service-icon kdm-premium-icon">E</div>
                        <h3 class="kdm-service-title">Entretien</h3>
                        <p class="kdm-service-description">
                          Service professionnel premium
                        </p>
                      </a>

                      <a href="/shop" class="kdm-service-card kdm-premium-card">
                        <div class="kdm-service-icon kdm-premium-icon">M</div>
                        <h3 class="kdm-service-title">Mobilier</h3>
                        <p class="kdm-service-description">
                          Collection haut de gamme
                        </p>
                      </a>
                    </div>

                    <div class="kdm-services-cta-wrapper">
                      <a href="/shop" class="kdm-services-cta kdm-premium-cta">
                        DÉCOUVRIR NOTRE COLLECTION
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Premium Partial Payment Section - KDMobilier Style -->
          <section class="pbmit-bg-color-light pbmit-element-miconheading-style-1">
            <div class="container">
              <div class="row">
                <div class="col-md-12">
                  <div class="pbmit-heading-subheading text-center">
                    <h4 class="pbmit-subtitle" style="color: #000;">FACILITÉ DE PAIEMENT</h4>
                    <h2 class="pbmit-title" style="color: #000;">
                    Facilité de Paiement
                    </h2>

                  </div>
                </div>
              </div>

              <div class="row g-4">
                <!-- Payment Features -->
                <div class="col-lg-4 col-md-6">
                  <div class="pbmit-ihbox pbmit-ihbox-style-16">
                    <div class="pbmit-ihbox-headingicon">
                      <div class="pbmit-ihbox-icon">
                        <div class="pbmit-ihbox-icon-wrapper">
                          <div class="pbmit-icon-type-icon">
                            <i class="fa fa-credit-card" style="color: var(--pbmit-global-color); font-size: 2rem;"></i>
                          </div>
                        </div>
                      </div>
                      <div class="pbmit-ihbox-contents">
                        <h2 class="pbmit-element-title">Acompte de 25%</h2>
                        <div class="pbmit-heading-desc">
                        Versez un acompte de 25% du montant de la commande
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-lg-4 col-md-6">
                  <div class="pbmit-ihbox pbmit-ihbox-style-16">
                    <div class="pbmit-ihbox-headingicon">
                      <div class="pbmit-ihbox-icon">
                        <div class="pbmit-ihbox-icon-wrapper">
                          <div class="pbmit-icon-type-icon">
                            <i class="fa fa-clock-o" style="color: var(--pbmit-global-color); font-size: 2rem;"></i>
                          </div>
                        </div>
                      </div>
                      <div class="pbmit-ihbox-contents">
                        <h2 class="pbmit-element-title">Paiement Flexible</h2>
                        <div class="pbmit-heading-desc">
                        Complétez vos paiements quand vous le souhaitez, à votre rythme
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-lg-4 col-md-6">
                  <div class="pbmit-ihbox pbmit-ihbox-style-16">
                    <div class="pbmit-ihbox-headingicon">
                      <div class="pbmit-ihbox-icon">
                        <div class="pbmit-ihbox-icon-wrapper">
                          <div class="pbmit-icon-type-icon">
                            <i class="fa fa-shield" style="color: var(--pbmit-global-color); font-size: 2rem;"></i>
                          </div>
                        </div>
                      </div>
                      <div class="pbmit-ihbox-contents">
                        <h2 class="pbmit-element-title">100% Sécurisé</h2>
                        <div class="pbmit-heading-desc">
                        Transactions sécurisées via Wave Money, leader du paiement mobile
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

        </div>
      </t>
    </xpath>
  </template>
</odoo>
