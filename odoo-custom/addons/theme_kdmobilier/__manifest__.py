# V1.1 - Testing automated deployment
{
    "name": "Theme KDMobilier",
    "version": "1.1.4",
    "author": "Hotcodes",
    "category": "Theme/Ecommerce",
    "description": "This is the first theme for kdmobilier",
    "depends": [
        "website",
        "website_blog",
        "website_sale",
        "website_sale_wishlist",
        "website_helpdesk",
        "website_mass_mailing",
        "website_sale_subscription",
        "account",
    ],
    "data": [
        "data/ir_assets.xml",
        "views/layout.xml",
        "views/header_template.xml",  # Header enabled
        "views/footer_template.xml",  # Footer enabled
        "views/kdmobilier_homepage.xml",  # Homepage enabled
        "views/kdmobilier_templates.xml",
        "views/blog_templates.xml",
        "views/blog_cover_template.xml",
        "views/blog_post_cover_template.xml",
        "views/category_nav_template.xml",
        "views/category_header_template.xml",
        # "views/category_image_fix.xml",
        "views/mobile_category_header.xml",
        "views/mega_menu_inline_fix.xml",
        "views/menues.xml",
        "views/contact_us.xml",
        "views/auth_templage.xml",
        "views/services.xml",
        "views/design-planning.xml",
        "views/entretien-canape.xml",
        "views/conseil-decoration.xml",
        "views/aboutus_page.xml",
        "views/contact_us_thank_you.xml",
        "data/website_data.xml",
        "views/auth_signup_templates_email.xml",
        "views/checkout_template.xml",
        "views/account_portal_templates.xml",
        "views/custom_nav_category_templates.xml",
        "views/search_result_template.xml",
        # "views/minimal_test.xml",  # Temporarily disabled
    ],
    "assets": {
        "web.assets_frontend": [
            # XML Templates
            # "theme_kdmobilier/static/src/xml/templates.xml",  # Temporarily disabled
            # GOOGLE FONTS
            "https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap",
            "https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300..700&display=swap",
            # Development assets
            "/theme_kdmobilier/static/src/js/dev_mode.js",
            # Production assets
            # CSS
            # "/theme_kdmobilier/static/src/css/main_1.css",
            # "/theme_kdmobilier/static/build/css/styles.css",
            # "/theme_kdmobilier/static/src/css/custom.css",
            # CSS
            # "theme_kdmobilier/static/src/css/aos.css",
            # "theme_kdmobilier/static/src/css/flaticon.css",
            # "theme_kdmobilier/static/src/css/fontawesome.css",
            # "theme_kdmobilier/static/src/css/magnific-popup.css",
            # "theme_kdmobilier/static/src/css/pbminfotech-base-icons.css",
            # "theme_kdmobilier/static/src/css/shortcode.css",
            "theme_kdmobilier/static/src/css/swiper.min.css",
            # "theme_kdmobilier/static/src/css/themify-icons.css",
            # "theme_kdmobilier/static/src/css/twentytwenty.css",
            "theme_kdmobilier/static/src/css/base.css",
            "theme_kdmobilier/static/src/css/responsive.css",
            # "theme_kdmobilier/static/src/css/bootstrap.min.css",
            # "theme_kdmobilier/static/src/css/checkout_summary.css"
            "theme_kdmobilier/static/src/scss/_fonts.scss",
            "theme_kdmobilier/static/src/css/style.css",
            "theme_kdmobilier/static/src/css/style_1.css",
            "theme_kdmobilier/static/src/css/premium-buttons.css",
            "theme_kdmobilier/static/src/css/mobile-improvements.css",
            "theme_kdmobilier/static/src/css/blog_custom.css",
            "theme_kdmobilier/static/src/css/all_fixes.css",
            "theme_kdmobilier/static/src/css/premium_products.css",
            "theme_kdmobilier/static/src/css/premium_products_inline.css",
            "theme_kdmobilier/static/src/css/category_header.css",
            "theme_kdmobilier/static/src/css/modal_fix.css",
            "theme_kdmobilier/static/src/css/checkout_summary.css",
            "theme_kdmobilier/static/src/css/checkout_wizard.css",
            "theme_kdmobilier/static/src/css/mega_menu_fix_modern.css",
            "theme_kdmobilier/static/src/css/currency_payment_features.css",  # Re-enabled
            "theme_kdmobilier/static/src/css/homepage_spacing.css",  # Homepage spacing optimizations
            "theme_kdmobilier/static/src/css/luxury_collection.css",  # Luxury collection section
            "theme_kdmobilier/static/src/css/services_payment_style.css",  # Services section with payment card style
            # "theme_kdmobilier/static/src/css/mobile_grid_final_override.css",  # Mobile grid final override - temporarily disabled
            "theme_kdmobilier/static/src/css/flag-icons.min.css",  # Flag icons for currency selector
            # "theme_kdmobilier/static/src/css/lodas-site.css",
            # JAVA SCRIPT
            "/theme_kdmobilier/static/build/js/main.js",
            "theme_kdmobilier/static/src/js/attributes.js",
            "theme_kdmobilier/static/src/js/poppup.js",
            "theme_kdmobilier/static/src/js/maps.js",
            "theme_kdmobilier/static/src/js/infinite_scroll.js",
            # "theme_kdmobilier/static/src/js/jquery.min.js",
            # "theme_kdmobilier/static/src/js/popper.min.js",
            # "theme_kdmobilier/static/src/js/bootstrap.min.js",
            # "theme_kdmobilier/static/src/js/jquery.waypoints.min.js",
            # "theme_kdmobilier/static/src/js/jquery.magnific-popup.min.js",
            # "theme_kdmobilier/static/src/js/jquery.appear.js",
            "theme_kdmobilier/static/src/js/swiper.min.js",
            # "theme_kdmobilier/static/src/js/circle-progress.min.js",
            # "theme_kdmobilier/static/src/js/isotope.pkgd.min.js",
            # "theme_kdmobilier/static/src/js/jquery.countdown.min.js",
            # "/theme_kdmobilier/static/src/js/aos.js",
            # "theme_kdmobilier/static/src/js/gsap.js",
            # "theme_kdmobilier/static/src/js/masonry.min.js",
            # "theme_kdmobilier/static/src/js/numinate.min.js",
            # "theme_kdmobilier/static/src/js/ScrollTrigger.js",
            # "theme_kdmobilier/static/src/js/SplitText.js",
            # "theme_kdmobilier/static/src/js/magnetic.js",
            # "theme_kdmobilier/static/src/js/morphext.min.js",
            # "theme_kdmobilier/static/src/js/gsap-animation.js",
            # "theme_kdmobilier/static/src/js/jquery.event.move.js",
            # "theme_kdmobilier/static/src/js/jquery.twentytwenty.js",
            # "theme_kdmobilier/static/src/js/jquery-validate/jquery.validate.min.js",
            # "theme_kdmobilier/static/src/js/scripts.js",
            "theme_kdmobilier/static/src/js/salon_products.js",
            "theme_kdmobilier/static/src/js/wishlist.js",
            "theme_kdmobilier/static/src/js/custom.js",
            "theme_kdmobilier/static/src/js/main.js",
            "theme_kdmobilier/static/src/js/dropdown_extension.js",
            "theme_kdmobilier/static/src/js/recaptcha_loader.js",
            "theme_kdmobilier/static/src/js/fix_modal_open.js",
            "theme_kdmobilier/static/src/js/checkout_responsive.js",
            "theme_kdmobilier/static/src/js/account_portal_sidebar.js",
            "theme_kdmobilier/static/src/js/currency_selector.js",  # Re-enabled
            # "theme_kdmobilier/static/src/js/mobile_grid_enforcer.js",  # Mobile grid enforcer - temporarily disabled
            "theme_kdmobilier/static/src/js/newsletter_subscription.js",
        ],
    },
    "installable": True,
    "application": False,
    "auto_install": False,
}
