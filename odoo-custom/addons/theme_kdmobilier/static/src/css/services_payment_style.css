/* ========================================
   SERVICES SECTION - PAYMENT CARD STYLE
   ======================================== */

/* Services Section - Payment Style */
.kdm-services-section.kdm-premium-cards {
  background: linear-gradient(135deg, #faf8f5 0%, #f5f2ed 100%);
  padding: 3rem 1rem;
  position: relative;
}

.kdm-services-section.kdm-premium-cards::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 30% 20%, 
    rgba(209, 157, 102, 0.03) 0%, 
    transparent 50%
  );
  pointer-events: none;
}

/* Services Header */
.kdm-services-header {
  margin-bottom: 3rem;
}

.kdm-services-title {
  font-family: var(--pbmit-heading-typography-font-family);
  font-size: 2.5rem;
  font-weight: 300;
  letter-spacing: 4px;
  color: var(--pbmit-heading-color);
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.kdm-services-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--pbmit-global-color), var(--pbmit-secondary-color));
  border-radius: 2px;
  margin-bottom: 0;
}

/* Payment-Style Grid */
.kdm-services-grid.kdm-payment-style-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 3rem;
}

/* Payment-Style Service Cards */
.kdm-service-card.kdm-payment-card {
  background: var(--pbmit-white-color);
  border-radius: 15px;
  padding: 2rem 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(209, 157, 102, 0.1);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 200px;
  justify-content: center;
}

.kdm-service-card.kdm-payment-card:hover,
.kdm-service-card.kdm-payment-card:focus {
  text-decoration: none;
  color: inherit;
}

/* Top gradient line like payment cards */
.kdm-service-card.kdm-payment-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, var(--pbmit-global-color), var(--pbmit-secondary-color));
  border-radius: 15px 15px 0 0;
}

/* Subtle background gradient */
.kdm-service-card.kdm-payment-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(252, 250, 247, 0.5) 0%,
    rgba(255, 255, 255, 0) 50%
  );
  pointer-events: none;
  z-index: 1;
}

/* Hover Effects */
.kdm-service-card.kdm-payment-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
  border-color: rgba(209, 157, 102, 0.2);
}

.kdm-service-card.kdm-payment-card:hover .kdm-service-icon-circle {
  transform: scale(1.1);
  box-shadow: 0 8px 20px rgba(209, 157, 102, 0.25);
}

/* Circular Icon Container */
.kdm-service-icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--pbmit-global-color), var(--pbmit-secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 5px 15px rgba(209, 157, 102, 0.2);
  position: relative;
  z-index: 2;
}

.kdm-service-icon-circle i {
  color: var(--pbmit-white-color);
  font-size: 1.8rem;
  transition: all 0.3s ease;
}

/* Service Title */
.kdm-service-card.kdm-payment-card .kdm-service-title {
  font-family: var(--pbmit-heading-typography-font-family);
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--pbmit-heading-color);
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 2;
}

/* Service Description */
.kdm-service-card.kdm-payment-card .kdm-service-description {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #666;
  margin: 0;
  position: relative;
  z-index: 2;
}

/* CTA Button */
.kdm-services-cta-wrapper {
  text-align: center;
}

.kdm-services-cta.kdm-premium-cta {
  display: inline-block;
  background: linear-gradient(135deg, var(--pbmit-global-color), var(--pbmit-secondary-color));
  color: var(--pbmit-white-color);
  padding: 1.25rem 2.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 8px 25px rgba(209, 157, 102, 0.3);
  position: relative;
  overflow: hidden;
}

.kdm-services-cta.kdm-premium-cta:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(209, 157, 102, 0.4);
  text-decoration: none;
  color: var(--pbmit-white-color);
}

.kdm-services-cta.kdm-premium-cta:active {
  transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .kdm-services-section.kdm-premium-cards {
    padding: 2rem 1rem;
  }
  
  .kdm-services-title {
    font-size: 1.8rem;
    letter-spacing: 2px;
  }
  
  .kdm-services-grid.kdm-payment-style-grid {
    gap: 1rem;
  }
  
  .kdm-service-card.kdm-payment-card {
    padding: 1.5rem 1rem;
    min-height: 160px;
  }
  
  .kdm-service-icon-circle {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }
  
  .kdm-service-icon-circle i {
    font-size: 1.4rem;
  }
  
  .kdm-service-card.kdm-payment-card .kdm-service-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }
  
  .kdm-service-card.kdm-payment-card .kdm-service-description {
    font-size: 0.8rem;
    line-height: 1.5;
  }
  
  .kdm-services-cta.kdm-premium-cta {
    padding: 1rem 2rem;
    font-size: 0.8rem;
    letter-spacing: 1.5px;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .kdm-services-section.kdm-premium-cards {
    padding: 1.5rem 0.75rem;
  }
  
  .kdm-services-title {
    font-size: 1.5rem;
    letter-spacing: 1px;
  }
  
  .kdm-services-grid.kdm-payment-style-grid {
    gap: 0.75rem;
  }
  
  .kdm-service-card.kdm-payment-card {
    padding: 1.25rem 0.75rem;
    min-height: 140px;
  }
  
  .kdm-service-icon-circle {
    width: 50px;
    height: 50px;
    margin-bottom: 0.75rem;
  }
  
  .kdm-service-icon-circle i {
    font-size: 1.2rem;
  }
  
  .kdm-service-card.kdm-payment-card .kdm-service-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  
  .kdm-service-card.kdm-payment-card .kdm-service-description {
    font-size: 0.75rem;
  }
}
