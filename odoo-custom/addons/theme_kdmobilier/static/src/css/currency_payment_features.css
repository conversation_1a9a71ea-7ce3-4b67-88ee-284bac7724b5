/* ========================================
   Currency Selector & Payment Features CSS
   ======================================== */

/* Currency Selector Styles - Enhanced */
.currency-selector-wrapper {
  margin-right: 15px;
  position: relative;
  z-index: 1000;
  overflow: visible;
}

.currency-selector-wrapper .dropdown {
  position: static;
}

/* Currency dropdown styling */
.currency-dropdown {
  min-width: 220px;
  max-width: 280px;
  overflow: visible;
  border: 1px solid rgba(200, 168, 130, 0.3);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
}

.currency-dropdown .dropdown-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  transition: all 0.2s ease;
}

.currency-dropdown .dropdown-item:hover {
  background-color: rgba(200, 168, 130, 0.1);
  color: #c8a882;
}

.currency-dropdown .dropdown-item.active {
  background-color: rgba(200, 168, 130, 0.15);
  color: #c8a882;
  font-weight: 500;
}

.currency-dropdown .currency-flag {
  width: 20px;
  height: 15px;
  flex-shrink: 0;
}

.currency-dropdown .currency-code {
  font-weight: 600;
  min-width: 35px;
  color: #333;
}

.currency-dropdown .currency-name {
  color: #666;
  font-size: 14px;
}

.currency-dropdown .currency-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.currency-dropdown .currency-info .currency-code {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  line-height: 1.2;
}

.currency-dropdown .currency-info .currency-name {
  color: #666;
  font-size: 12px;
  line-height: 1.2;
}

/* Hide desktop currency selector on mobile */
@media (max-width: 991.98px) {
  .currency-selector-wrapper {
    display: none !important;
  }
}

/* Mobile Currency Selector - Enhanced */
.mobile-currency-selector {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 20px !important;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin: 10px 15px 20px 15px !important;
}

.mobile-currency-btn {
  width: 100%;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: #fff !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
}

.mobile-currency-btn .currency-text {
  color: #fff !important;
  text-shadow: none;
  font-weight: 600;
}

.mobile-currency-btn .currency-flag {
  width: 24px !important;
  height: 18px !important;
}

.mobile-currency-dropdown {
  width: 100% !important;
  min-width: auto !important;
  left: 0 !important;
  right: auto !important;
  background: rgba(0, 0, 0, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  margin-top: 5px !important;
}

.mobile-currency-dropdown .currency-option {
  color: #fff !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 14px 16px !important;
  font-size: 14px !important;
}

.mobile-currency-dropdown .currency-option:hover {
  background: rgba(255, 255, 255, 0.15) !important;
}

.mobile-currency-dropdown .currency-option.active {
  background: rgba(200, 168, 130, 0.2) !important;
  color: #c8a882 !important;
  border-left: 3px solid #c8a882;
}

/* Floating Currency Selector - Modern Mobile UX */
.floating-currency-selector {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  opacity: 0.9;
  transition: all 0.3s ease;
}

.floating-currency-selector .dropup {
  position: relative;
}

/* Force dropup behavior ONLY for mobile floating selector - Maximum override power */
.floating-currency-selector .dropdown-menu,
.floating-currency-selector .floating-currency-dropdown,
.floating-currency-selector .dropup .dropdown-menu {
  position: fixed !important;
  bottom: 80px !important;
  right: 20px !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
  will-change: auto !important;
  margin: 0 !important;
  z-index: 10000 !important;
  min-width: 200px !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(200, 168, 130, 0.3) !important;
  border-radius: 12px !important;
  box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.2) !important;
  padding: 8px 0 !important;
}

.floating-currency-selector .dropdown-menu.show,
.floating-currency-selector .floating-currency-dropdown.show,
.floating-currency-selector .dropup .dropdown-menu.show {
  position: fixed !important;
  bottom: 80px !important;
  right: 20px !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
  margin: 0 !important;
  display: block !important;
}

/* Override ALL possible Bootstrap positioning for floating selector */
.floating-currency-selector[data-bs-popper] .dropdown-menu,
.floating-currency-selector .dropdown-menu[data-bs-popper],
.floating-currency-selector .dropdown-menu[style],
.floating-currency-selector .floating-currency-dropdown[style] {
  position: fixed !important;
  bottom: 80px !important;
  right: 20px !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
  inset: auto !important;
}

/* Hide floating selector on desktop to avoid conflicts */
@media (min-width: 992px) {
  .floating-currency-selector {
    display: none !important;
  }
}

.floating-currency-selector:hover {
  opacity: 1;
  transform: scale(1.05);
}

.floating-currency-btn {
  width: 60px;
  height: 60px;
  border-radius: 50% !important;
  background: #fff !important;
  border: 2px solid rgba(200, 168, 130, 0.3) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  color: var(--pbmit-global-color) !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 0 !important;
  margin: 0 !important;
  position: relative;
}

.floating-currency-btn::after {
  display: none !important;
}

.floating-currency-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25) !important;
  background: #fff !important;
  border-color: var(--pbmit-global-color) !important;
}

.floating-currency-btn .currency-flag {
  width: 18px !important;
  height: 13px !important;
  border-radius: 2px;
  margin: 0 auto 2px auto !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  display: block !important;
  flex-shrink: 0;
}

.floating-currency-btn .currency-code {
  font-size: 9px !important;
  font-weight: 700 !important;
  line-height: 1 !important;
  text-shadow: none;
  letter-spacing: 0.5px;
  display: block !important;
  text-align: center !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 100%;
}

.floating-currency-dropdown {
  position: absolute !important;
  bottom: calc(100% + 10px) !important;
  right: 0 !important;
  left: auto !important;
  top: auto !important;
  min-width: 200px !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(200, 168, 130, 0.3) !important;
  border-radius: 12px !important;
  box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.2) !important;
  padding: 8px 0 !important;
  transform: none !important;
  margin: 0 !important;
  z-index: 10000 !important;
  inset: auto 0px auto auto !important;
}

.floating-currency-dropdown .currency-option {
  padding: 12px 16px !important;
  color: #333 !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  transition: all 0.2s ease !important;
}

.floating-currency-dropdown .currency-option:hover {
  background: linear-gradient(
    135deg,
    rgba(200, 168, 130, 0.15) 0%,
    rgba(212, 175, 55, 0.1) 100%
  ) !important;
  color: var(--pbmit-global-color) !important;
  transform: translateX(5px);
}

.floating-currency-dropdown .currency-option.active {
  background: linear-gradient(
    135deg,
    rgba(200, 168, 130, 0.2) 0%,
    rgba(212, 175, 55, 0.15) 100%
  ) !important;
  color: var(--pbmit-global-color) !important;
  border-left: 3px solid var(--pbmit-global-color) !important;
  font-weight: 600;
}

.floating-currency-dropdown .currency-flag {
  width: 18px !important;
  height: 14px !important;
  margin-right: 10px;
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Hide floating selector on desktop */
@media (min-width: 992px) {
  .floating-currency-selector {
    display: none !important;
  }
}

.currency-selector-btn {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  border: 2px solid rgba(255, 255, 255, 0.8);
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 10px 18px;
  border-radius: 25px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  min-width: 180px;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Remove Bootstrap's default dropdown arrow to prevent double arrows */
.currency-selector-btn::after,
.mobile-currency-btn::after,
.floating-currency-btn::after {
  display: none !important;
}

/* Currency flag styling */
.currency-flag {
  width: 20px;
  height: 15px;
  border-radius: 2px;
  display: inline-block;
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
}

/* Currency text styling */
.currency-text {
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 0.85rem;
  white-space: nowrap;
  color: #2c3e50 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.currency-selector-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.currency-selector-btn:hover::before {
  left: 100%;
}

.currency-selector-btn:hover,
.currency-selector-btn:focus,
.currency-selector-btn:active,
.currency-selector-btn.show {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0.98) 100%
  ) !important;
  border-color: var(--pbmit-global-color) !important;
  color: #2c3e50 !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
  transform: translateY(-2px);
}

/* Dropdown arrow styling */
.currency-selector-btn .dropdown-arrow {
  font-size: 0.7rem;
  transition: transform 0.3s ease;
  opacity: 0.8;
}

.currency-selector-btn.show .dropdown-arrow,
.currency-selector-btn[aria-expanded="true"] .dropdown-arrow {
  transform: rotate(180deg);
  opacity: 1;
}

.currency-dropdown {
  min-width: 220px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  padding: 12px 0;
  margin-top: 8px;
  z-index: 9999 !important;
  position: absolute !important;
  transform: none !important;
  top: calc(100% + 5px) !important;
  right: 0 !important;
  left: auto !important;
  inset: auto 0px auto auto !important;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.currency-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) !important;
}

/* Fix for desktop dropdown li padding - Override header styles */
@media (min-width: 1201px) {
  .currency-dropdown li,
  .currency-selector-wrapper .dropdown-menu li,
  .header-style-2 .currency-dropdown li,
  .header-style-3 .currency-dropdown li {
    padding: 0 !important;
    width: 100% !important;
    margin: 0 !important;
  }

  /* Ensure currency options take full width on desktop */
  .currency-dropdown .currency-option,
  .currency-selector-wrapper .dropdown-menu .currency-option {
    width: 100% !important;
    padding: 16px 20px !important;
    margin: 0 !important;
    display: flex !important;
  }
}

.currency-option {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  color: #2c3e50;
  border: none;
  background: none;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.currency-option::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(200, 168, 130, 0.1),
    transparent
  );
  transition: left 0.4s ease;
}

.currency-option:hover::before {
  left: 100%;
}

.currency-option:last-child {
  border-bottom: none;
}

.currency-option:hover,
.currency-option:focus {
  background: linear-gradient(
    135deg,
    rgba(200, 168, 130, 0.15) 0%,
    rgba(212, 175, 55, 0.1) 100%
  ) !important;
  color: var(--pbmit-global-color) !important;
  text-decoration: none;
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(200, 168, 130, 0.3);
}

.currency-option.active {
  background: linear-gradient(
    135deg,
    rgba(200, 168, 130, 0.15) 0%,
    rgba(212, 175, 55, 0.1) 100%
  );
  color: var(--pbmit-global-color);
  font-weight: 700;
  border-left: 4px solid var(--pbmit-global-color);
}

.currency-option.active:hover {
  background: linear-gradient(
    135deg,
    rgba(200, 168, 130, 0.15) 0%,
    rgba(212, 175, 55, 0.1) 100%
  ) !important;
  color: var(--pbmit-global-color) !important;
}

/* Premium Flag Icons Styling */
.currency-flag {
  margin-right: 15px;
  width: 28px !important;
  height: 21px !important;
  border-radius: 6px;
  display: inline-block !important;
  vertical-align: middle;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  overflow: hidden;
  flex-shrink: 0;
}

/* Override flag-icons default styling for our use case */
.currency-flag.fi {
  line-height: 21px !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

.currency-option:hover .currency-flag {
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
  transform: scale(1.05);
}

.currency-code {
  font-weight: 700;
  margin-right: 12px;
  min-width: 45px;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
}

.currency-name {
  color: #666;
  font-size: 0.9rem;
  flex: 1;
  font-weight: 500;
}

.currency-option:hover .currency-name,
.currency-option:focus .currency-name {
  color: var(--pbmit-global-color);
}

.currency-option.active .currency-name {
  color: var(--pbmit-global-color);
  font-weight: 600;
}

.currency-option.active:hover .currency-name {
  color: var(--pbmit-global-color);
}

/* Payment Methods Footer Styles */
.payment-methods-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 25px 0;
  margin-bottom: 20px;
}

.payment-methods-title {
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0;
  letter-spacing: 0.5px;
}

.payment-methods-logos {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 15px;
  flex-wrap: wrap;
}

.payment-logo {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 18px 22px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(200, 168, 130, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-logo:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-color: rgba(200, 168, 130, 0.4);
}

.payment-logo img {
  height: 60px !important;
  width: auto !important;
  max-width: 120px !important;
  object-fit: contain !important;
  opacity: 0.9 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  filter: none !important;
  /* No filters - show natural colors */
}

.payment-logo:hover img {
  opacity: 1 !important;
  transform: scale(1.08) !important;
  filter: none !important;
  /* No filters - keep natural colors */
}

/* Responsive payment methods */
@media (max-width: 768px) {
  .payment-methods-logos {
    justify-content: center;
    margin-top: 20px;
    gap: 12px;
  }

  .payment-methods-section .row {
    text-align: center;
  }

  .payment-methods-title {
    text-align: center;
    margin-bottom: 20px;
  }

  .payment-logo {
    padding: 14px 18px;
  }

  .payment-logo img {
    height: 50px;
    max-width: 110px;
  }
}

.payment-methods-logos {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 18px;
  flex-wrap: wrap;
}

.payment-logo {
  background: #fff;
  border-radius: 8px;
  padding: 8px 12px;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.payment-logo:hover {
  transform: translateY(-2px);
}

.payment-logo img {
  height: 24px;
  width: auto;
  max-width: 60px;
  object-fit: contain;
}

/* Partial Payment Section Styles */
.partial-payment-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.partial-payment-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
}

.partial-payment-content {
  position: relative;
  z-index: 2;
}

.section-badge {
  display: inline-block;
  background: var(--pbmit-global-color);
  color: #fff;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 20px;
}

.partial-payment-title {
  font-size: 3rem;
  font-weight: 300;
  color: #000;
  margin-bottom: 25px;
  line-height: 1.2;
  font-family: var(--pbmit-heading-typography-font-family);
}

.highlight-text {
  color: var(--pbmit-global-color);
  font-weight: 600;
}

.partial-payment-description {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.8;
  margin-bottom: 40px;
  font-weight: 300;
}

/* Payment Features */
.payment-features {
  margin-bottom: 40px;
}

.payment-feature {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;
}

.feature-icon {
  background: var(--pbmit-global-color);
  color: #fff;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.feature-icon i {
  font-size: 1.2rem;
}

.feature-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #000;
  margin-bottom: 5px;
}

.feature-content p {
  color: #666;
  margin-bottom: 0;
  font-size: 0.95rem;
}

/* Payment CTA Buttons */
.payment-cta {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.btn-primary-custom {
  background: var(--pbmit-global-color);
  color: #fff;
  padding: 15px 30px;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  border: 2px solid var(--pbmit-global-color);
}

.btn-primary-custom:hover {
  background: transparent;
  color: var(--pbmit-global-color);
  transform: translateY(-2px);
}

.btn-secondary-custom {
  background: transparent;
  color: #000;
  padding: 15px 30px;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 500;
  border: 2px solid #000;
  transition: all 0.3s ease;
}

.btn-secondary-custom:hover {
  background: #000;
  color: #fff;
  transform: translateY(-2px);
}

/* Payment Visual Section */
.partial-payment-visual {
  position: relative;
  z-index: 2;
}

.payment-cards-stack {
  position: relative;
  margin-bottom: 40px;
}

.payment-card {
  background: #fff;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  position: relative;
  transition: transform 0.3s ease;
}

.payment-card:hover {
  transform: translateX(10px);
}

.card-1 {
  background: linear-gradient(135deg, var(--pbmit-global-color), #4a90e2);
  color: #fff;
  transform: rotate(-2deg);
}

.card-2 {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: #fff;
  transform: rotate(1deg);
  margin-left: 30px;
}

.card-3 {
  background: linear-gradient(135deg, #6f42c1, #e83e8c);
  color: #fff;
  transform: rotate(-1deg);
  margin-left: 60px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.card-title {
  font-weight: 600;
  font-size: 1.1rem;
}

.card-amount {
  font-size: 1.5rem;
  font-weight: 700;
}

.card-date {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Payment Benefits - Enhanced */
.payment-benefits {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.pbmit-payment-benefits {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.pbmit-payment-benefits .pbmit-list {
  flex-grow: 1;
  margin-bottom: 2rem;
}

.pbmit-payment-benefits .pbmit-list li {
  margin-bottom: 1rem;
  font-size: 1rem;
  line-height: 1.5;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  color: #000;
}

.benefit-item i {
  color: var(--pbmit-global-color);
  font-size: 1.1rem;
}

/* Additional Premium Styling */
.partial-payment-section .container {
  position: relative;
  z-index: 2;
}

/* Animated gradient background for cards */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.payment-card.card-1 {
  background: linear-gradient(
    -45deg,
    var(--pbmit-global-color),
    #4a90e2,
    #667eea,
    #764ba2
  );
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

.payment-card.card-2 {
  background: linear-gradient(-45deg, #28a745, #20c997, #17a2b8, #6f42c1);
  background-size: 400% 400%;
  animation: gradientShift 10s ease infinite;
}

.payment-card.card-3 {
  background: linear-gradient(-45deg, #6f42c1, #e83e8c, #fd7e14, #ffc107);
  background-size: 400% 400%;
  animation: gradientShift 12s ease infinite;
}

/* Enhanced hover effects */
.payment-feature:hover .feature-icon {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.payment-logo:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

/* Currency selector enhanced styling */
.currency-selector-btn {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.currency-dropdown {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
}

/* Header z-index fixes - Enhanced */
.pbmit-pre-header-wrapper {
  position: relative;
  z-index: 1050 !important;
}

.pbmit-main-header-area {
  position: relative;
  z-index: 1040 !important;
}

.site-header {
  position: relative;
  z-index: 1030 !important;
}

/* Currency dropdown specific fixes */
.currency-selector-wrapper {
  position: relative;
  z-index: 1060 !important;
}

.currency-selector-wrapper .dropdown {
  position: relative;
  z-index: 1060 !important;
}

/* Ensure dropdown appears above everything */
.currency-selector-wrapper .dropdown-menu {
  z-index: 9999 !important;
  position: absolute !important;
  top: 100% !important;
  left: auto !important;
  right: 0 !important;
  transform: none !important;
  will-change: auto !important;
}

/* Fix for dropdown positioning conflicts */
.navbar-nav .dropdown-menu {
  z-index: 1050;
}

/* Ensure pre-header content doesn't get clipped */
.pbmit-pre-header-wrapper {
  overflow: visible !important;
}

.pbmit-pre-header-right {
  position: relative;
  z-index: 1060 !important;
}

/* Currency change notification */
.currency-change-notification {
  z-index: 10000;
}

.currency-change-notification .alert {
  border-radius: 8px;
  border: none;
  font-size: 0.9rem;
}

/* Additional Bootstrap dropdown fixes */
.currency-selector-wrapper .dropdown-menu.show {
  display: block !important;
  position: absolute !important;
  z-index: 9999 !important;
  top: 100% !important;
  right: 0 !important;
  left: auto !important;
  transform: none !important;
  margin: 0 !important;
}

/* Prevent dropdown from being clipped by overflow hidden */
.pbmit-pre-header-wrapper,
.pbmit-pre-header-right,
.pbmit-contact-info {
  overflow: visible !important;
}

/* Ensure dropdown button has proper positioning context */
.currency-selector-wrapper .dropdown {
  position: relative !important;
}

.currency-selector-wrapper .dropdown-toggle {
  position: relative !important;
  z-index: 1061 !important;
}

/* Premium Mobile Responsive Design */
@media (max-width: 991px) {
  .partial-payment-title {
    font-size: 2.5rem;
  }

  .payment-methods-logos {
    justify-content: center;
    margin-top: 15px;
  }

  .payment-cards-stack .payment-card {
    margin-left: 0 !important;
    transform: none !important;
  }

  .payment-cta {
    justify-content: center;
  }

  /* Currency selector mobile improvements */
  .currency-selector-btn {
    padding: 8px 14px;
    font-size: 0.85rem;
    min-width: 100px;
  }

  .currency-dropdown {
    min-width: 200px;
  }

  .currency-flag {
    width: 24px !important;
    height: 18px !important;
  }
}

@media (max-width: 767px) {
  .partial-payment-section {
    padding: 60px 0;
  }

  .partial-payment-title {
    font-size: 2rem;
  }

  .currency-selector-wrapper {
    margin-right: 8px;
  }

  .payment-methods-title {
    text-align: center;
    margin-bottom: 15px;
  }

  .payment-logo img {
    height: 20px;
  }

  .payment-cta {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary-custom,
  .btn-secondary-custom {
    width: 100%;
    justify-content: center;
    max-width: 280px;
  }

  /* Mobile currency selector */
  .currency-selector-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    min-width: 90px;
    border-radius: 20px;
  }

  .currency-dropdown {
    position: fixed !important;
    top: auto !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    border-radius: 20px 20px 0 0 !important;
    max-height: 60vh;
    overflow-y: auto;
    margin: 0 !important;
    box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.3) !important;
  }

  .currency-option {
    padding: 18px 20px;
    font-size: 1.1rem;
  }

  .currency-flag {
    width: 26px !important;
    height: 20px !important;
    margin-right: 12px;
  }

  .currency-code {
    font-size: 1rem;
    min-width: 50px;
  }

  .currency-name {
    font-size: 1rem;
  }
}

/* Fix for button cutoff and ensure proper spacing */
.pbmit-pre-header-wrapper {
  padding: 8px 0 !important;
  min-height: 50px;
  overflow: visible !important;
}

.pbmit-contact-info {
  align-items: center;
  height: 100%;
  overflow: visible !important;
}

.pbmit-contact-info li {
  display: flex;
  align-items: center;
  height: 100%;
}

/* Ensure dropdown doesn't get cut off */
.currency-selector-wrapper {
  position: relative;
  z-index: 1070 !important;
}

.currency-selector-wrapper .dropdown-menu {
  margin-top: 4px !important;
}

/* Premium animations for mobile */
@media (max-width: 767px) {
  .currency-dropdown.show {
    animation: slideUpMobile 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes slideUpMobile {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* ========================================
   PREMIUM KDMOBILIER PAYMENT SECTION
   ======================================== */

/* Top Payment Features Cards */
.pbmit-ihbox-style-16 {
  background: var(--pbmit-white-color);
  border-radius: 15px;
  padding: 30px 25px;
  box-shadow: 0 10px 30px rgba(var(--pbmit-blackish-color-rgb), 0.08);
  border: 1px solid rgba(var(--pbmit-global-color-rgb), 0.1);
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.pbmit-ihbox-style-16::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
}

.pbmit-ihbox-style-16:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(var(--pbmit-blackish-color-rgb), 0.12);
}

.pbmit-ihbox-style-16 .pbmit-ihbox-headingicon {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 100%;
}

.pbmit-ihbox-style-16 .pbmit-ihbox-icon {
  margin-bottom: 20px;
}

.pbmit-ihbox-style-16 .pbmit-ihbox-icon-wrapper {
  width: 70px;
  height: 70px;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 8px 25px rgba(var(--pbmit-global-color-rgb), 0.3);
}

.pbmit-ihbox-style-16 .pbmit-icon-type-icon i {
  color: var(--pbmit-white-color) !important;
  font-size: 2rem !important;
}

.pbmit-ihbox-style-16 .pbmit-element-title {
  font-family: var(--pbmit-heading-typography-font-family);
  font-weight: 700;
  color: var(--pbmit-heading-color);
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.pbmit-ihbox-style-16 .pbmit-heading-desc {
  color: var(--pbmit-body-typography-color);
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

/* KDMobilier Premium Payment Flow Styles */
.pbmit-payment-flow {
  position: relative;
  padding: 20px 0;
  min-height: 665px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.pbmit-payment-card {
  background: var(--pbmit-white-color);
  border-radius: 15px;
  padding: 25px 20px;
  margin-bottom: 15px;
  box-shadow: 0 10px 30px rgba(var(--pbmit-blackish-color-rgb), 0.08);
  border: 1px solid rgba(var(--pbmit-global-color-rgb), 0.1);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.pbmit-payment-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(
    90deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
}

.pbmit-payment-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 70px rgba(var(--pbmit-blackish-color-rgb), 0.15);
}

.pbmit-payment-initial::before {
  background: linear-gradient(90deg, var(--pbmit-global-color), #e6b800);
}

.pbmit-payment-flexible::before {
  background: linear-gradient(
    90deg,
    var(--pbmit-secondary-color),
    var(--pbmit-global-color)
  );
}

/* Payment Card Header */
.pbmit-payment-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.pbmit-payment-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--pbmit-white-color);
  font-size: 20px;
  box-shadow: 0 6px 20px rgba(var(--pbmit-global-color-rgb), 0.25);
}

.pbmit-payment-icon i {
  color: var(--pbmit-white-color) !important;
}

/* Override inline styles for payment card icons */
.pbmit-payment-card .pbmit-payment-icon i,
.pbmit-payment-card i[style*="color"] {
  color: var(--pbmit-white-color) !important;
}

.pbmit-payment-header h3 {
  font-family: var(--pbmit-heading-typography-font-family);
  font-weight: 700;
  color: var(--pbmit-heading-color);
  margin: 0;
  font-size: 1.2rem;
}

/* Payment Amount Display */
.pbmit-payment-amount {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  font-family: var(--pbmit-heading-typography-font-family);
}

.pbmit-payment-desc {
  color: var(--pbmit-body-typography-color);
  font-size: 1rem;
  margin-bottom: 15px;
  line-height: 1.5;
}

/* Payment Method Badge */
.pbmit-payment-method {
  background: rgba(var(--pbmit-global-color-rgb), 0.1);
  padding: 8px 15px;
  border-radius: 8px;
  border: 1px solid rgba(var(--pbmit-global-color-rgb), 0.2);
  text-align: center;
}

.pbmit-payment-method span {
  color: var(--pbmit-global-color);
  font-weight: 600;
  font-size: 1rem;
}

/* Payment Options */
.pbmit-payment-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.pbmit-payment-options span {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--pbmit-body-typography-color);
  font-size: 0.95rem;
}

.pbmit-payment-options i {
  color: var(--pbmit-global-color);
  font-size: 14px;
}

/* Payment Arrow Animation */
.pbmit-payment-arrow {
  text-align: center;
  margin: 10px 0;
  color: var(--pbmit-global-color);
  font-size: 1.5rem;
  animation: pbmit-bounce 2s infinite;
}

@keyframes pbmit-bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Payment Benefits Section */
.pbmit-payment-benefits {
  padding: 20px 0;
  min-height: 665px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Height matching for payment flow and benefits sections */
.row.align-items-center .col-lg-6 {
  display: flex;
  flex-direction: column;
}

.row.align-items-center .col-lg-6 > div {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pbmit-payment-benefits .pbmit-element-title {
  color: var(--pbmit-heading-color);
  font-family: var(--pbmit-heading-typography-font-family);
  font-weight: 700;
  margin-bottom: 25px;
  font-size: 1.8rem;
}

.pbmit-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pbmit-list li {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(var(--pbmit-global-color-rgb), 0.1);
  color: var(--pbmit-body-typography-color);
  font-size: 1rem;
  line-height: 1.6;
}

.pbmit-list li:last-child {
  border-bottom: none;
}

.pbmit-list li i {
  color: var(--pbmit-global-color);
  font-size: 18px;
  flex-shrink: 0;
}

/* Payment CTA Buttons - Improved Layout */
.pbmit-payment-cta {
  display: flex;
  gap: 25px;
  flex-wrap: wrap;
  align-items: stretch;
}

.pbmit-payment-cta .pbmit-btn {
  padding: 15px 25px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 200px;
  white-space: nowrap;
  flex: 1;
}

.pbmit-payment-cta .pbmit-btn:hover {
  transform: translateY(-2px);
  text-decoration: none;
}

/* Ensure buttons don't overlap */
.pbmit-payment-cta .pbmit-btn .pbmit-button-content-wrapper {
  width: 100%;
  text-align: center;
}

/* Button Width Fixes - Reduce Large Button Width */
.pbmit-btn {
  max-width: 250px;
  width: auto;
  display: inline-block;
  text-align: center;
  white-space: nowrap;
  padding: 13px 24px;
  margin: 0 48px 10px 0;
}

/* Desktop Text Content - Show desktop text, hide mobile text */
@media (min-width: 768px) {
  .pbmit-payment-description .desktop-text {
    display: block !important;
  }

  .pbmit-payment-description .mobile-text {
    display: none !important;
  }

  .pbmit-ihbox-style-16 .pbmit-heading-desc .desktop-text {
    display: block !important;
  }

  .pbmit-ihbox-style-16 .pbmit-heading-desc .mobile-text {
    display: none !important;
  }

  .pbmit-payment-benefits .pbmit-list .desktop-text {
    display: block !important;
  }

  .pbmit-payment-benefits .pbmit-list .mobile-text {
    display: none !important;
  }
}

/* Responsive Design for Payment Section - Tablet */
@media (max-width: 991px) {
  /* Reset height matching on tablet */
  .row.align-items-center .col-lg-6 {
    display: block;
  }

  .row.align-items-center .col-lg-6 > div {
    flex: none;
    display: block;
  }

  .pbmit-payment-flow {
    height: auto;
    min-height: auto;
    display: block;
    background: #f8f9fa;
    border-radius: 16px;
    padding: 25px 20px;
    margin-bottom: 20px;
  }

  .pbmit-payment-benefits {
    height: auto;
    min-height: auto;
    display: block;
    background: #f8f9fa;
    border-radius: 16px;
    padding: 25px 20px;
  }

  .pbmit-payment-card {
    background: white;
    border-radius: 12px;
    padding: 25px 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: none;
    flex: none;
    display: block;
  }

  /* Top feature cards responsive */
  .pbmit-ihbox-style-16 {
    background: white;
    border-radius: 12px;
    margin-bottom: 16px;
    height: auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: none;
  }

  .pbmit-payment-amount {
    font-size: 3rem;
  }

  .pbmit-payment-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
  }

  .pbmit-payment-benefits .pbmit-element-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
  }

  .pbmit-payment-cta {
    justify-content: center;
    gap: 20px;
  }

  .pbmit-payment-cta .pbmit-btn {
    max-width: 220px !important;
    min-width: 140px !important;
    width: auto !important;
    margin: 0 45px 8px 0 !important;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  }
}

@media (max-width: 767px) {
  /* Mobile App Style Container */
  .pbmit-payment-flow,
  .pbmit-payment-benefits {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 20px 15px;
    margin-bottom: 20px;
  }

  /* Mobile App Style - 2 Column Grid for Payment Flow */
  .pbmit-payment-flow {
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    background: #f8f9fa;
    border-radius: 20px;
    padding: 15px;
    margin-bottom: 20px;
  }

  .pbmit-payment-card {
    background: white;
    border-radius: 16px;
    padding: 12px 8px;
    margin-bottom: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 140px;
    width: 100%;
  }

  /* Mobile responsive text for payment cards */
  .pbmit-payment-card h3,
  .pbmit-payment-card .pbmit-element-title {
    font-size: 0.85rem !important;
    line-height: 1.2 !important;
    margin-bottom: 6px !important;
    font-weight: 600 !important;
  }

  .pbmit-payment-card .pbmit-element-content,
  .pbmit-payment-card p {
    font-size: 0.7rem !important;
    line-height: 1.1 !important;
    margin-bottom: 4px !important;
  }

  .pbmit-payment-card .percentage-text {
    font-size: 1.8rem !important;
    font-weight: 700 !important;
    margin: 8px 0 !important;
  }

  .pbmit-payment-card .btn {
    font-size: 0.7rem !important;
    padding: 6px 12px !important;
    margin-top: 8px !important;
  }

  /* Mobile feature cards - Only for payment section */
  .pbmit-element-section .row.g-4 {
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin: 0 !important;
  }

  .pbmit-element-section .row.g-4 .col-lg-4,
  .pbmit-element-section .row.g-4 .col-md-6 {
    padding: 0 !important;
    margin: 0 !important;
  }

  .pbmit-element-section .pbmit-ihbox-style-16 {
    background: white;
    border-radius: 16px;
    padding: 16px 12px;
    margin-bottom: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 160px;
  }

  /* Third card spans full width in 2-column grid */
  .pbmit-element-section .row.g-4 .col-lg-4:nth-child(3) {
    grid-column: 1 / -1;
  }

  .pbmit-element-section .row.g-4 .col-lg-4:nth-child(3) .pbmit-ihbox-style-16 {
    max-width: 300px;
    margin: 0 auto;
  }

  .pbmit-ihbox-style-16 .pbmit-ihbox-icon-wrapper {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    margin-bottom: 8px;
  }

  .pbmit-ihbox-style-16 .pbmit-icon-type-icon i {
    font-size: 1.3rem !important;
  }

  .pbmit-ihbox-style-16 .pbmit-element-title {
    font-size: 0.9rem;
    margin-bottom: 6px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.2;
  }

  .pbmit-ihbox-style-16 .pbmit-heading-desc {
    font-size: 0.75rem;
    color: #6c757d;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Mobile Payment Header - Compact for 2-column */
  .pbmit-payment-header {
    gap: 8px;
    margin-bottom: 10px;
    align-items: center;
    flex-direction: column;
  }

  .pbmit-payment-header h3 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    text-align: center;
    line-height: 1.2;
  }

  .pbmit-payment-icon {
    width: 45px;
    height: 45px;
    font-size: 18px;
    border-radius: 12px;
    margin-bottom: 6px;
  }

  /* Mobile Amount Display - Compact */
  .pbmit-payment-amount {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 6px;
    color: var(--pbmit-global-color);
    line-height: 1;
  }

  /* Mobile Description - Compact */
  .pbmit-payment-description {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 12px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Mobile Text Content - Hide desktop text, show mobile text */
  .pbmit-payment-description .desktop-text {
    display: none !important;
  }

  .pbmit-payment-description .mobile-text {
    display: block !important;
  }

  .pbmit-ihbox-style-16 .pbmit-heading-desc .desktop-text {
    display: none !important;
  }

  .pbmit-ihbox-style-16 .pbmit-heading-desc .mobile-text {
    display: block !important;
  }

  /* Mobile Benefits List - Show mobile version */
  .pbmit-payment-benefits .pbmit-list .desktop-text {
    display: none !important;
  }

  .pbmit-payment-benefits .pbmit-list .mobile-text {
    display: block !important;
  }

  /* Mobile Payment Method Badge - Compact */
  .pbmit-payment-method {
    padding: 8px 12px;
    font-size: 0.75rem;
    border-radius: 10px;
    background: rgba(var(--pbmit-global-color-rgb), 0.1);
    color: var(--pbmit-global-color);
    border: 1px solid rgba(var(--pbmit-global-color-rgb), 0.2);
    text-align: center;
    font-weight: 500;
    margin-top: 8px;
    width: 100%;
  }

  /* Mobile Benefits Section */
  .pbmit-payment-benefits {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 20px 15px;
    margin-top: 20px;
  }

  /* Mobile Benefits List - Compact */
  .pbmit-payment-benefits .pbmit-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 0;
  }

  .pbmit-payment-benefits .pbmit-list li {
    margin-bottom: 0;
    font-size: 0.8rem;
    padding: 8px;
    color: #6c757d;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 60px;
  }

  .pbmit-payment-benefits .pbmit-list li i {
    font-size: 0.9rem !important;
    margin-right: 6px;
    color: var(--pbmit-global-color);
  }

  /* Mobile Buttons - Native App Style 2-Column Grid */
  .pbmit-btn {
    max-width: none !important;
    width: 100% !important;
    padding: 12px 16px !important;
    margin: 0 0 8px 0 !important;
    display: block;
    text-align: center !important;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* Mobile CTA Container - Stack buttons vertically */
  .pbmit-payment-cta {
    flex-direction: column !important;
    gap: 8px !important;
    margin-top: 16px;
    grid-column: 1 / -1; /* Span full width across both columns */
  }

  /* Mobile Section Titles */
  .pbmit-element-title {
    font-size: 1.4rem !important;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
  }

  /* Mobile Container Spacing */
  .container-fluid {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  /* Mobile Row Spacing */
  .row {
    margin-left: -8px !important;
    margin-right: -8px !important;
  }

  .row > [class*="col-"] {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }

  /* Mobile Section Spacing */
  .pbmit-element-section {
    padding: 30px 0 !important;
  }

  /* Remove any arrows or decorative elements on mobile */
  .pbmit-payment-arrow,
  .pbmit-payment-flow-arrow {
    display: none !important;
  }

  /* Mobile Typography Adjustments */
  .pbmit-payment-header h3 {
    font-size: 1.1rem;
  }

  /* ========================================
     MOBILE NATIVE APP CLASSES - NEW
     ======================================== */

  /* Mobile App Native Style - New Classes */
  .pbmit-payment-flow.mobile-native {
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    background: transparent;
    border-radius: 0;
    padding: 0;
    margin-bottom: 20px;
  }

  .pbmit-payment-card.mobile-compact {
    background: white;
    border-radius: 12px;
    padding: 6px 4px;
    margin-bottom: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 110px;
    width: 100%;
  }

  /* Mobile compact text styles */
  .mobile-compact h3,
  .mobile-compact .pbmit-payment-header h3 {
    font-size: 0.7rem !important;
    line-height: 1.1 !important;
    margin-bottom: 3px !important;
    font-weight: 600 !important;
  }

  .mobile-compact .pbmit-payment-amount {
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    margin: 4px 0 !important;
    color: var(--pbmit-global-color);
  }

  .mobile-compact .pbmit-payment-description,
  .mobile-compact .pbmit-payment-method {
    font-size: 0.6rem !important;
    line-height: 1.1 !important;
    margin-bottom: 2px !important;
  }

  .mobile-compact .pbmit-payment-options {
    font-size: 0.55rem !important;
    line-height: 1 !important;
  }

  .mobile-compact .pbmit-payment-icon {
    width: 25px;
    height: 25px;
    margin-bottom: 3px;
  }

  .mobile-compact .pbmit-payment-icon i {
    font-size: 1rem !important;
  }

  .mobile-compact .pbmit-payment-options .d-flex {
    margin-bottom: 1px !important;
  }

  .mobile-compact .pbmit-payment-options i {
    font-size: 0.5rem !important;
    margin-right: 3px !important;
  }

  /* Mobile Card Hover Effects */
  .pbmit-payment-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .pbmit-ihbox-style-16:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  /* Mobile Button Active States */
  .pbmit-btn:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .pbmit-payment-cta {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .pbmit-payment-cta .pbmit-btn {
    max-width: none !important;
    width: 100% !important;
    justify-content: center;
    min-width: auto !important;
    margin: 0 0 15px 0 !important;
    display: block;
  }

  /* Improve mobile spacing for payment benefits */
  .pbmit-payment-benefits .pbmit-element-title {
    font-size: 1.4rem;
    margin-bottom: 1rem;
  }

  .pbmit-payment-benefits .pbmit-list li {
    margin-bottom: 0.8rem;
  }
}

/* Desktop Styles - Restore normal text sizes and layout */
@media (min-width: 768px) {
  /* Reset payment card text sizes for desktop */
  .pbmit-payment-card h3,
  .pbmit-payment-card .pbmit-element-title {
    font-size: 1.1rem !important;
    line-height: 1.4 !important;
    margin-bottom: 10px !important;
  }

  .pbmit-payment-card .pbmit-element-content,
  .pbmit-payment-card p {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    margin-bottom: 8px !important;
  }

  .pbmit-payment-card .percentage-text {
    font-size: 2.5rem !important;
    margin: 15px 0 !important;
  }

  .pbmit-payment-card .btn {
    font-size: 0.9rem !important;
    padding: 10px 20px !important;
    margin-top: 15px !important;
  }

  .pbmit-payment-card {
    min-height: 220px;
    padding: 20px 16px;
  }

  /* Reset grid layout for desktop */
  .pbmit-payment-flow {
    display: flex !important;
    gap: 20px;
    background: transparent;
    border-radius: 0;
    padding: 0;
  }

  .pbmit-element-section .row.g-4 {
    display: flex !important;
    flex-wrap: wrap;
    margin: 0 -12px !important;
  }

  .pbmit-element-section .row.g-4 .col-lg-4,
  .pbmit-element-section .row.g-4 .col-md-6 {
    padding: 0 12px !important;
    flex: 0 0 auto;
    width: 33.333333%;
  }

  .pbmit-element-section .pbmit-ihbox-style-16 {
    min-height: 200px;
    padding: 30px 20px;
  }

  /* Reset benefits section for desktop */
  .pbmit-payment-benefits {
    background: transparent;
    border-radius: 0;
    padding: 0;
    margin-top: 0;
  }

  .pbmit-payment-benefits .pbmit-list {
    display: block;
    margin-bottom: 2rem;
  }

  .pbmit-payment-benefits .pbmit-list li {
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    text-align: left;
    min-height: auto;
    padding: 15px 0;
    margin-bottom: 15px;
    font-size: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
}

/* Extra small screens - full width buttons */
@media (max-width: 480px) {
  .pbmit-btn {
    max-width: none !important;
    width: 100% !important;
    padding: 13px 16px !important;
    margin: 0 0 12px 0 !important;
    display: block;
    text-align: center !important;
  }
}

/* ========================================
   LUXURY COLLECTION SECTION - PREMIUM DESIGN
   ======================================== */

.premium-collection-luxury {
  position: relative;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  overflow: hidden;
  padding: 80px 0;
}

.luxury-collection-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(200, 168, 130, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(200, 168, 130, 0.03) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.luxury-collection-top-border,
.luxury-collection-bottom-border {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--pbmit-global-color) 20%,
    var(--pbmit-global-color) 80%,
    transparent 100%
  );
}

.luxury-collection-top-border {
  top: 0;
}

.luxury-collection-bottom-border {
  bottom: 0;
}

.luxury-floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.luxury-element {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--pbmit-global-color);
  border-radius: 50%;
  opacity: 0.2;
  animation: luxuryFloat 8s ease-in-out infinite;
}

.luxury-element-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.luxury-element-2 {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.luxury-element-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes luxuryFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 0.4;
  }
}

/* Luxury Section Header */
.luxury-section-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.luxury-section-badge {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, var(--pbmit-global-color), #d4af37);
  color: white;
  padding: 8px 24px;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 600;
  letter-spacing: 1px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(200, 168, 130, 0.3);
}

.luxury-badge-icon {
  font-size: 0.7rem;
  animation: luxurySparkle 2s ease-in-out infinite;
}

@keyframes luxurySparkle {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.luxury-section-title {
  font-family: var(--pbmit-heading-typography-font-family);
  margin-bottom: 25px;
  position: relative;
}

.luxury-title-main {
  display: block;
  font-size: 2.8rem;
  font-weight: 700;
  color: #2c3e50;
  letter-spacing: 2px;
  line-height: 1.1;
}

.luxury-title-subtitle {
  display: block;
  font-size: 1.8rem;
  font-weight: 300;
  color: var(--pbmit-global-color);
  letter-spacing: 4px;
  font-style: italic;
  margin-top: 5px;
}

.luxury-title-underline {
  position: relative;
  margin: 15px auto;
  width: 80px;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--pbmit-global-color),
    transparent
  );
}

.luxury-underline-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--pbmit-global-color);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(200, 168, 130, 0.5);
}

.luxury-section-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
  font-weight: 300;
}

/* Luxury Products Grid */
.luxury-products-showcase {
  position: relative;
  z-index: 2;
}

/* Luxury products grid styles moved to luxury_collection.css to avoid conflicts */

/* ========================================
   PREMIUM FOOTER PAYMENT METHODS REDESIGN
   ======================================== */

/* Enhanced Payment Methods Section in Footer */
.payment-methods-section {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
  padding: 40px 0 !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.payment-methods-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="payment-dots" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23c8a882" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23payment-dots)"/></svg>');
  pointer-events: none;
}

.payment-methods-title {
  color: white !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  text-align: center;
  margin-bottom: 30px !important;
  position: relative;
  z-index: 2;
}

.payment-methods-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color) 0%,
    var(--pbmit-secondary-color) 100%
  );
  border-radius: 2px;
}

.payment-methods-logos {
  display: flex !important;
  justify-content: center !important;
  align-items: center;
  gap: 30px !important;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.payment-logo {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px !important;
  padding: 15px 20px !important;
  transition: all 0.4s ease !important;
  position: relative;
  overflow: hidden;
}

.payment-logo::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(200, 168, 130, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.payment-logo:hover::before {
  left: 100%;
}

.payment-logo:hover {
  transform: translateY(-5px) scale(1.05) !important;
  box-shadow: 0 10px 30px rgba(200, 168, 130, 0.2) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(200, 168, 130, 0.3) !important;
}

/* Payment Methods Section Premium Background */
.payment-methods-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 20px !important;
  padding: 25px !important;
  margin: 20px 0 !important;
  border: 1px solid rgba(200, 168, 130, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* Payment Methods Section Text Colors */
.payment-methods-section,
.payment-methods-section * {
  color: #333 !important;
}

.payment-methods-title {
  color: #222 !important;
  font-weight: 600 !important;
}

/* ========================================
   SERVICES SECTION - PAYMENT CARD STYLE
   ======================================== */

/* Services Section Container - Payment Style */
.kdm-services-section.kdm-premium-cards {
  background: linear-gradient(135deg, #faf8f5 0%, #f5f2ed 100%);
  padding: 3rem 1rem;
  position: relative;
}

.kdm-services-section.kdm-premium-cards::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 30% 20%,
    rgba(209, 157, 102, 0.03) 0%,
    transparent 50%
  );
  pointer-events: none;
}

/* Legacy Services Section Container */
.kdm-services-section {
  background: linear-gradient(135deg, #faf8f5 0%, #f5f2ed 100%);
  padding: 2rem 1rem;
  position: relative;
}

/* Mobile App-like Minimal Styles */
.kdm-services-section.kdm-mobile-minimal {
  padding: 1rem 0.75rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
}

.kdm-services-section.kdm-mobile-minimal .container {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  max-width: 100%;
}

.kdm-services-section.kdm-mobile-minimal .row {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.kdm-services-section.kdm-mobile-minimal .row > * {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.kdm-services-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="0.5" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="0.5" fill="%23000" opacity="0.02"/><circle cx="50" cy="10" r="0.3" fill="%23000" opacity="0.015"/><circle cx="10" cy="60" r="0.4" fill="%23000" opacity="0.02"/><circle cx="90" cy="40" r="0.3" fill="%23000" opacity="0.015"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

/* Premium Hero Gallery - Mobile First */
.kdm-hero-gallery {
  position: relative;
  height: 240px;
  margin-bottom: 2.5rem;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  gap: 0;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 24px rgba(0, 0, 0, 0.1);
}

.kdm-hero-main {
  position: relative;
  overflow: hidden;
  grid-column: 1;
  grid-row: 1;
  border-radius: 24px;
}

.kdm-hero-main img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.kdm-hero-main:hover img {
  transform: scale(1.05);
}

.kdm-hero-grid {
  display: none;
}

/* Premium Image Effects */
.kdm-hero-secondary {
  position: relative;
}

.kdm-hero-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  border-radius: inherit;
}

.kdm-hero-secondary:hover::before {
  opacity: 1;
}

/* Premium Glass Effect */
.kdm-hero-main::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  pointer-events: none;
  border-radius: inherit;
}

.kdm-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 40%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

.kdm-hero-content {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  z-index: 2;
}

.kdm-hero-title {
  color: #fff;
  font-size: 2rem;
  font-weight: 200;
  letter-spacing: 4px;
  font-family: var(--pbmit-heading-typography-font-family);
  margin-bottom: 0.5rem;
  line-height: 1.1;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  text-transform: uppercase;
}

.kdm-hero-subtitle {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1rem;
  font-weight: 300;
  letter-spacing: 2px;
  margin: 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  font-style: italic;
}

/* Services Content */
.kdm-services-content {
  padding: 0;
  position: relative;
  z-index: 1;
}

.kdm-services-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

/* Mobile-First Minimal Services Header */
.kdm-mobile-minimal .kdm-services-header {
  margin-bottom: 1rem;
}

/* Extra Small Screens - Ultra Compact */
@media (max-width: 480px) {
  .kdm-mobile-minimal .kdm-services-section {
    padding: 0.25rem 0.125rem;
  }

  .kdm-mobile-minimal .container {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }

  .kdm-mobile-minimal .kdm-services-grid {
    gap: 0.125rem;
  }

  .kdm-mobile-minimal .kdm-service-card {
    padding: 0.375rem;
    border-radius: 8px;
  }

  .kdm-mobile-minimal .kdm-service-icon {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
    margin-bottom: 0.3rem;
  }

  .kdm-mobile-minimal .kdm-service-title {
    font-size: 0.55rem;
    margin-bottom: 0.2rem;
    letter-spacing: 0.2px;
  }

  .kdm-mobile-minimal .kdm-service-description {
    font-size: 0.5rem;
    line-height: 1.2;
  }

  .kdm-mobile-minimal .kdm-services-title {
    font-size: 1.1rem;
    letter-spacing: 1.5px;
    margin-bottom: 0.5rem;
  }

  .kdm-mobile-minimal .kdm-services-cta {
    font-size: 0.7rem;
    padding: 0.75rem 1.25rem;
    letter-spacing: 1px;
    margin-top: 0.75rem;
  }
}

.kdm-services-title {
  font-size: 1.6rem;
  margin-bottom: 1rem;
  font-weight: 300;
  letter-spacing: 3px;
  color: #2c2c2c;
  font-family: var(--pbmit-heading-typography-font-family);
  text-transform: uppercase;
  position: relative;
}

.kdm-services-divider {
  width: 80px;
  height: 2px;
  background: linear-gradient(
    90deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  margin: 0 auto;
  border-radius: 1px;
  position: relative;
}

.kdm-services-divider::after {
  content: "";
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: var(--pbmit-global-color);
  border-radius: 50%;
}

/* Payment-Style Grid */
.kdm-services-grid.kdm-payment-style-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 3rem;
}

/* Premium Mobile Grid */
.kdm-services-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

/* Mobile-First Minimal Grid */
.kdm-mobile-minimal .kdm-services-grid {
  gap: 0.25rem;
  margin-bottom: 1rem;
}

/* Payment-Style Service Cards */
.kdm-service-card.kdm-payment-card {
  background: var(--pbmit-white-color);
  border-radius: 15px;
  padding: 2rem 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(209, 157, 102, 0.1);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 200px;
  justify-content: center;
}

.kdm-service-card.kdm-payment-card:hover,
.kdm-service-card.kdm-payment-card:focus {
  text-decoration: none;
  color: inherit;
}

/* Top gradient line like payment cards */
.kdm-service-card.kdm-payment-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(
    90deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  border-radius: 15px 15px 0 0;
}

/* Subtle background gradient */
.kdm-service-card.kdm-payment-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(252, 250, 247, 0.5) 0%,
    rgba(255, 255, 255, 0) 50%
  );
  pointer-events: none;
  z-index: 1;
}

/* Hover Effects */
.kdm-service-card.kdm-payment-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
  border-color: rgba(209, 157, 102, 0.2);
}

.kdm-service-card.kdm-payment-card:hover .kdm-service-icon-circle {
  transform: scale(1.1);
  box-shadow: 0 8px 20px rgba(209, 157, 102, 0.25);
}

/* Premium Service Cards */
.kdm-service-card {
  background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%);
  border-radius: 16px;
  padding: 1rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

/* Service Card Link Styles */
.kdm-service-card {
  text-decoration: none;
  color: inherit;
}

.kdm-service-card:hover,
.kdm-service-card:focus {
  text-decoration: none;
  color: inherit;
}

/* Mobile-First Minimal Service Cards */
.kdm-mobile-minimal .kdm-service-card {
  padding: 1rem 0.75rem;
  border-radius: 18px;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 120px;
  position: relative;
  overflow: hidden;
  text-decoration: none !important;
  color: inherit !important;
}

.kdm-mobile-minimal .kdm-service-card:hover,
.kdm-mobile-minimal .kdm-service-card:focus {
  text-decoration: none !important;
  color: inherit !important;
}

.kdm-service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  border-radius: 20px 20px 0 0;
}

.kdm-service-card::after {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(212, 175, 55, 0.03) 0%,
    transparent 70%
  );
  pointer-events: none;
}

.kdm-service-card:active {
  transform: translateY(2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* Circular Icon Container for Payment Style */
.kdm-service-icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 5px 15px rgba(209, 157, 102, 0.2);
  position: relative;
  z-index: 2;
}

.kdm-service-icon-circle i {
  color: var(--pbmit-white-color);
  font-size: 1.8rem;
  transition: all 0.3s ease;
}

/* Payment-Style Service Title */
.kdm-service-card.kdm-payment-card .kdm-service-title {
  font-family: var(--pbmit-heading-typography-font-family);
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--pbmit-heading-color);
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 2;
}

/* Payment-Style Service Description */
.kdm-service-card.kdm-payment-card .kdm-service-description {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #666;
  margin: 0;
  position: relative;
  z-index: 2;
}

/* Payment-Style CTA Button */
.kdm-services-cta-wrapper {
  text-align: center;
}

.kdm-services-cta.kdm-premium-cta {
  display: inline-block;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  color: var(--pbmit-white-color);
  padding: 1.25rem 2.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 8px 25px rgba(209, 157, 102, 0.3);
  position: relative;
  overflow: hidden;
}

.kdm-services-cta.kdm-premium-cta:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(209, 157, 102, 0.4);
  text-decoration: none;
  color: var(--pbmit-white-color);
}

.kdm-services-cta.kdm-premium-cta:active {
  transform: translateY(-1px);
}

.kdm-service-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  color: #fff;
  font-weight: 600;
  font-family: var(--pbmit-heading-typography-font-family);
  letter-spacing: 1px;
  box-shadow: 0 3px 12px rgba(var(--pbmit-global-color-rgb), 0.3);
  position: relative;
  z-index: 1;
  flex-shrink: 0;
}

/* Mobile-First Minimal Service Icons */
.kdm-mobile-minimal .kdm-service-icon {
  width: 40px;
  height: 40px;
  font-size: 1rem;
  margin-bottom: 0.6rem;
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  ) !important;
  box-shadow: 0 3px 12px rgba(var(--pbmit-global-color-rgb), 0.25);
  display: flex !important;
  align-items: center;
  justify-content: center;
  color: #fff !important;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.kdm-service-title {
  font-size: 0.7rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: #2c2c2c;
  font-family: var(--pbmit-heading-typography-font-family);
  line-height: 1.2;
  text-transform: uppercase;
  position: relative;
  z-index: 1;
}

/* Mobile-First Minimal Service Titles */
.kdm-mobile-minimal .kdm-service-title {
  font-size: 0.7rem;
  margin-bottom: 0.3rem;
  letter-spacing: 0.5px;
  line-height: 1.2;
  font-weight: 600 !important;
  color: #1a1a1a !important;
  text-transform: uppercase;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

.kdm-service-description {
  color: #666;
  line-height: 1.4;
  font-weight: 400;
  margin: 0;
  font-size: 0.65rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* Mobile-First Minimal Service Descriptions */
.kdm-mobile-minimal .kdm-service-description {
  font-size: 0.6rem;
  line-height: 1.4;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  color: #666 !important;
  font-weight: 400 !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Premium CTA Button */
.kdm-services-cta {
  display: block;
  width: 100%;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  );
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  padding: 1.25rem 2rem;
  border-radius: 16px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(var(--pbmit-global-color-rgb), 0.25),
    0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 1.5rem;
  position: relative;
  overflow: hidden;
  font-family: var(--pbmit-heading-typography-font-family);
}

/* Mobile-First Minimal CTA Button */
.kdm-mobile-minimal .kdm-services-cta {
  font-size: 0.85rem;
  padding: 1rem 1.75rem;
  letter-spacing: 1px;
  margin-top: 1.25rem;
  border-radius: 16px;
  background: linear-gradient(
    135deg,
    var(--pbmit-global-color),
    var(--pbmit-secondary-color)
  ) !important;
  border: none !important;
  color: #fff !important;
  font-weight: 600 !important;
  text-transform: uppercase;
  box-shadow: 0 4px 16px rgba(var(--pbmit-global-color-rgb), 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

/* Mobile Touch Interactions */
.kdm-mobile-minimal .kdm-service-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.06) !important;
}

.kdm-mobile-minimal .kdm-services-cta:active {
  transform: scale(0.96);
  box-shadow: 0 2px 8px rgba(var(--pbmit-global-color-rgb), 0.4);
}

/* Mobile-First Minimal Services Header */
.kdm-mobile-minimal .kdm-services-header {
  margin-bottom: 1rem;
}

.kdm-mobile-minimal .kdm-services-title {
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
  letter-spacing: 1px;
  font-weight: 700 !important;
  color: #1a1a1a !important;
  text-transform: uppercase;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

.kdm-services-cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.kdm-services-cta:hover::before {
  left: 100%;
}

.kdm-services-cta:hover,
.kdm-services-cta:focus {
  color: #fff;
  text-decoration: none;
  transform: translateY(-3px);
  box-shadow: 0 12px 48px rgba(var(--pbmit-global-color-rgb), 0.35),
    0 8px 24px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.kdm-services-cta:active {
  transform: translateY(-1px);
  box-shadow: 0 6px 24px rgba(var(--pbmit-global-color-rgb), 0.3),
    0 2px 12px rgba(0, 0, 0, 0.1);
}

/* Tablet Styles */
@media (min-width: 768px) {
  /* Restore normal padding on tablet and up */
  .kdm-services-section.kdm-mobile-minimal {
    padding: 4rem 2rem;
  }

  .kdm-services-section.kdm-mobile-minimal .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .kdm-services-section.kdm-mobile-minimal .row {
    margin-left: -15px;
    margin-right: -15px;
  }

  .kdm-services-section.kdm-mobile-minimal .row > * {
    padding-left: 15px;
    padding-right: 15px;
  }

  .kdm-mobile-minimal .kdm-services-grid {
    gap: 1rem;
    margin-bottom: 3rem;
  }

  .kdm-mobile-minimal .kdm-service-card {
    padding: 1.25rem;
    border-radius: 20px;
  }

  .kdm-mobile-minimal .kdm-service-icon {
    width: 44px;
    height: 44px;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    border-radius: 14px;
  }

  .kdm-mobile-minimal .kdm-service-title {
    font-size: 0.8rem;
    letter-spacing: 1px;
    margin-bottom: 0.75rem;
  }

  .kdm-mobile-minimal .kdm-service-description {
    font-size: 0.7rem;
    line-height: 1.5;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .kdm-mobile-minimal .kdm-services-cta {
    font-size: 1rem;
    padding: 1.375rem 2.25rem;
    border-radius: 18px;
    letter-spacing: 2.5px;
    margin-top: 1.5rem;
  }

  .kdm-mobile-minimal .kdm-services-header {
    margin-bottom: 3rem;
  }

  .kdm-mobile-minimal .kdm-services-title {
    font-size: 1.8rem;
    letter-spacing: 4px;
    margin-bottom: 1rem;
  }

  .kdm-services-section {
    padding: 4rem 2rem;
  }

  .kdm-hero-gallery {
    height: 300px;
    margin-bottom: 0;
    border-radius: 28px;
    grid-template-columns: 1.8fr 1fr;
    gap: 10px;
  }

  .kdm-hero-grid {
    display: grid;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 8px;
  }

  .kdm-hero-secondary {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  .kdm-hero-secondary img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
  }

  .kdm-hero-secondary:hover img {
    transform: scale(1.03);
  }

  .kdm-hero-content {
    bottom: 2.5rem;
    left: 2.5rem;
    right: 2.5rem;
  }

  .kdm-hero-title {
    font-size: 2.5rem;
    letter-spacing: 5px;
  }

  .kdm-hero-subtitle {
    font-size: 1.1rem;
    letter-spacing: 2px;
  }

  .kdm-services-header {
    margin-bottom: 3rem;
  }

  .kdm-services-title {
    font-size: 1.8rem;
    letter-spacing: 4px;
  }

  .kdm-services-divider {
    width: 100px;
  }

  .kdm-services-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .kdm-service-card {
    padding: 1.25rem;
    border-radius: 20px;
  }

  .kdm-service-icon {
    width: 44px;
    height: 44px;
    border-radius: 14px;
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .kdm-service-title {
    font-size: 0.8rem;
    letter-spacing: 1px;
    margin-bottom: 0.75rem;
  }

  .kdm-service-description {
    font-size: 0.7rem;
    line-height: 1.5;
  }

  .kdm-services-cta {
    font-size: 1rem;
    padding: 1.375rem 2.25rem;
    border-radius: 18px;
    letter-spacing: 2.5px;
  }
}

/* Desktop Styles - Professional Computer Interface */
@media (min-width: 992px) {
  /* Desktop Professional Layout */
  .kdm-services-section.kdm-mobile-minimal {
    padding: 3rem 2rem;
    background: linear-gradient(135deg, #fafafa 0%, #f8f8f8 100%);
  }

  .kdm-services-section.kdm-mobile-minimal .container {
    padding-left: 30px;
    padding-right: 30px;
    max-width: 1400px;
  }

  .kdm-services-section.kdm-mobile-minimal .row {
    margin-left: -20px;
    margin-right: -20px;
  }

  .kdm-services-section.kdm-mobile-minimal .row > * {
    padding-left: 20px;
    padding-right: 20px;
  }

  .kdm-mobile-minimal .kdm-services-header {
    text-align: left;
    margin-bottom: 3rem;
  }

  .kdm-mobile-minimal .kdm-services-title {
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 4px;
    margin-bottom: 1rem;
    color: #1a1a1a;
    text-transform: uppercase;
  }

  .kdm-mobile-minimal .kdm-services-divider {
    width: 100px;
    height: 3px;
    margin: 0;
    background: linear-gradient(
      90deg,
      var(--pbmit-global-color),
      var(--pbmit-secondary-color)
    );
  }

  /* Desktop: Professional List Layout */
  .kdm-mobile-minimal .kdm-services-grid {
    display: block !important;
    margin-bottom: 3rem;
  }

  .kdm-mobile-minimal .kdm-service-card {
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 0 !important;
    padding: 2rem 0 2rem 3rem !important;
    box-shadow: none !important;
    border: none !important;
    border-left: 4px solid var(--pbmit-global-color) !important;
    margin-bottom: 1.5rem !important;
    backdrop-filter: blur(10px) !important;
    aspect-ratio: unset !important;
    display: block !important;
    text-align: left !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  /* Desktop: Hide icons for professional look */
  .kdm-mobile-minimal .kdm-service-icon {
    display: none !important;
  }

  .kdm-mobile-minimal .kdm-service-title {
    font-size: 1.3rem !important;
    font-weight: 500 !important;
    letter-spacing: 2px !important;
    margin-bottom: 0.8rem !important;
    text-transform: uppercase !important;
    color: #1a1a1a !important;
    line-height: 1.3 !important;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif !important;
  }

  .kdm-mobile-minimal .kdm-service-description {
    font-size: 1rem !important;
    line-height: 1.7 !important;
    display: block !important;
    -webkit-line-clamp: unset !important;
    line-clamp: unset !important;
    -webkit-box-orient: unset !important;
    overflow: visible !important;
    color: #555 !important;
    font-weight: 300 !important;
    margin: 0 !important;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif !important;
    max-width: 85%;
  }

  .kdm-mobile-minimal .kdm-services-cta {
    font-size: 1rem;
    padding: 1.2rem 2.5rem;
    border-radius: 6px;
    letter-spacing: 1.5px;
    margin-top: 3rem;
    background: linear-gradient(
      135deg,
      var(--pbmit-global-color),
      var(--pbmit-secondary-color)
    );
    border: none;
    color: #fff;
    font-weight: 500;
    text-transform: uppercase;
    box-shadow: 0 4px 16px rgba(var(--pbmit-global-color-rgb), 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .kdm-mobile-minimal .kdm-services-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(var(--pbmit-global-color-rgb), 0.4);
  }

  /* Desktop: Professional hover effects */
  .kdm-mobile-minimal .kdm-service-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(var(--pbmit-global-color-rgb), 0.05) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .kdm-mobile-minimal .kdm-service-card:hover {
    background: rgba(255, 255, 255, 0.95) !important;
    transform: translateX(8px) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08) !important;
    border-left-color: var(--pbmit-secondary-color) !important;
  }

  .kdm-mobile-minimal .kdm-service-card:hover::before {
    opacity: 1;
  }

  .kdm-mobile-minimal .kdm-service-card:active {
    transform: translateX(4px) !important;
  }

  .kdm-services-section {
    padding: 6rem 1rem;
  }

  .kdm-hero-gallery {
    height: 350px;
    border-radius: 32px;
    grid-template-columns: 2fr 1fr;
    gap: 14px;
  }

  .kdm-hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 14px;
  }

  .kdm-hero-grid .kdm-hero-secondary:nth-child(3) {
    grid-column: 1 / -1;
  }

  .kdm-hero-secondary {
    border-radius: 20px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  }

  .kdm-hero-main {
    border-radius: 32px;
  }

  .kdm-hero-content {
    bottom: 3.5rem;
    left: 3.5rem;
    max-width: 75%;
  }

  .kdm-hero-title {
    font-size: 3.5rem;
    font-weight: 200;
    letter-spacing: 6px;
  }

  .kdm-hero-subtitle {
    font-size: 1.3rem;
    letter-spacing: 3px;
  }

  .kdm-services-header {
    text-align: left;
    margin-bottom: 4rem;
  }

  .kdm-services-title {
    font-size: 2rem;
    font-weight: 300;
    letter-spacing: 4px;
  }

  .kdm-services-divider {
    margin: 1.5rem 0 0 0;
    width: 120px;
  }

  .kdm-services-grid {
    display: block;
  }

  .kdm-service-card {
    background: linear-gradient(
      145deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(255, 255, 255, 0.4) 100%
    );
    border-radius: 0;
    padding: 2rem 0 2rem 2rem;
    box-shadow: none;
    border: none;
    border-left: 4px solid var(--pbmit-global-color);
    margin-bottom: 2.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .kdm-service-card::before {
    display: none;
  }

  .kdm-service-card::after {
    display: none;
  }

  .kdm-service-card:hover {
    background: linear-gradient(
      145deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.6) 100%
    );
    transform: translateX(8px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }

  .kdm-service-card:active {
    transform: translateX(4px);
  }

  .kdm-service-icon {
    display: none;
  }

  .kdm-service-title {
    font-size: 1.4rem;
    font-weight: 500;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 1rem;
    color: #2c2c2c;
  }

  .kdm-service-description {
    font-size: 1.05rem;
    font-weight: 300;
    line-height: 1.8;
    display: block;
    -webkit-line-clamp: unset;
    line-clamp: unset;
    -webkit-box-orient: unset;
    overflow: visible;
    color: #555;
  }

  .kdm-services-cta {
    display: inline-block;
    width: auto;
    background: linear-gradient(
      135deg,
      var(--pbmit-global-color),
      var(--pbmit-secondary-color)
    );
    color: #fff;
    font-weight: 500;
    font-size: 1.1rem;
    letter-spacing: 3px;
    padding: 1.25rem 3rem;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(var(--pbmit-global-color-rgb), 0.25),
      0 4px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    margin-top: 2rem;
    overflow: hidden;
  }

  .kdm-services-cta::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.6s ease;
  }

  .kdm-services-cta:hover::before {
    left: 100%;
  }

  .kdm-services-cta:hover,
  .kdm-services-cta:focus {
    color: #fff;
    transform: translateY(-4px);
    box-shadow: 0 12px 48px rgba(var(--pbmit-global-color-rgb), 0.35),
      0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .kdm-services-cta:active {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(var(--pbmit-global-color-rgb), 0.3),
      0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

/* Premium Gallery System - Centered Layout */
.kdm-premium-gallery {
  margin: 1.5rem auto;
  max-width: 600px;
  width: 100%;
}

.kdm-premium-gallery.kdm-design-gallery {
  margin-bottom: 2rem;
}

.kdm-premium-gallery.kdm-architecture-gallery {
  margin-top: 2rem;
}

.kdm-premium-image {
  position: relative;
  height: 280px;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.kdm-premium-image:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2), 0 12px 40px rgba(0, 0, 0, 0.15);
}

.kdm-premium-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.kdm-premium-image:hover img {
  transform: scale(1.08);
}

.kdm-premium-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(200, 168, 130, 0.85) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.kdm-premium-image:hover .kdm-premium-overlay {
  opacity: 0.95;
}

.kdm-premium-content {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  color: white;
  z-index: 2;
  text-align: center;
}

.kdm-premium-title {
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 0.5rem;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  letter-spacing: 3px;
  font-family: var(--pbmit-heading-typography-font-family);
  text-transform: uppercase;
}

.kdm-premium-subtitle {
  font-size: 1.1rem;
  opacity: 0.95;
  margin: 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  font-weight: 300;
  letter-spacing: 1px;
  font-style: italic;
}

/* Mobile Premium Gallery - Show single gallery */
@media (max-width: 767px) {
  .kdm-premium-gallery {
    margin: 1rem auto;
    max-width: 100%;
  }

  .kdm-premium-gallery.kdm-architecture-gallery {
    display: none;
  }

  .kdm-premium-image {
    height: 220px;
    border-radius: 20px;
  }

  .kdm-premium-content {
    bottom: 1.5rem;
    left: 1.5rem;
    right: 1.5rem;
  }

  .kdm-premium-title {
    font-size: 1.6rem;
    letter-spacing: 2px;
  }

  .kdm-premium-subtitle {
    font-size: 0.9rem;
  }
}

/* Tablet Premium Gallery */
@media (min-width: 768px) and (max-width: 1199px) {
  .kdm-premium-gallery {
    max-width: 500px;
    margin: 1.5rem auto;
  }

  .kdm-premium-image {
    height: 260px;
    border-radius: 22px;
  }

  .kdm-premium-title {
    font-size: 1.8rem;
    letter-spacing: 2.5px;
  }

  .kdm-premium-subtitle {
    font-size: 1rem;
  }
}

/* ========================================
   PAYMENT-STYLE SERVICES - MOBILE RESPONSIVE
   ======================================== */

/* Mobile Responsive for Payment-Style Cards */
@media (max-width: 768px) {
  .kdm-services-section.kdm-premium-cards {
    padding: 2rem 1rem;
  }

  .kdm-services-title {
    font-size: 1.8rem;
    letter-spacing: 2px;
  }

  .kdm-services-grid.kdm-payment-style-grid {
    gap: 1rem;
  }

  .kdm-service-card.kdm-payment-card {
    padding: 1.5rem 1rem;
    min-height: 160px;
  }

  .kdm-service-icon-circle {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }

  .kdm-service-icon-circle i {
    font-size: 1.4rem;
  }

  .kdm-service-card.kdm-payment-card .kdm-service-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .kdm-service-card.kdm-payment-card .kdm-service-description {
    font-size: 0.8rem;
    line-height: 1.5;
  }

  .kdm-services-cta.kdm-premium-cta {
    padding: 1rem 2rem;
    font-size: 0.8rem;
    letter-spacing: 1.5px;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .kdm-services-section.kdm-premium-cards {
    padding: 1.5rem 0.75rem;
  }

  .kdm-services-title {
    font-size: 1.5rem;
    letter-spacing: 1px;
  }

  .kdm-services-grid.kdm-payment-style-grid {
    gap: 0.75rem;
  }

  .kdm-service-card.kdm-payment-card {
    padding: 1.25rem 0.75rem;
    min-height: 140px;
  }

  .kdm-service-icon-circle {
    width: 50px;
    height: 50px;
    margin-bottom: 0.75rem;
  }

  .kdm-service-icon-circle i {
    font-size: 1.2rem;
  }

  .kdm-service-card.kdm-payment-card .kdm-service-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .kdm-service-card.kdm-payment-card .kdm-service-description {
    font-size: 0.75rem;
  }
}

/* Desktop Premium Gallery */
@media (min-width: 1200px) {
  .kdm-premium-gallery {
    max-width: 700px;
    margin: 2rem auto;
  }

  .kdm-premium-gallery.kdm-design-gallery {
    margin-bottom: 2.5rem;
  }

  .kdm-premium-gallery.kdm-architecture-gallery {
    margin-top: 2.5rem;
  }

  .kdm-premium-image {
    height: 320px;
    border-radius: 28px;
  }

  .kdm-premium-content {
    bottom: 2.5rem;
    left: 2.5rem;
    right: 2.5rem;
  }

  .kdm-premium-title {
    font-size: 2.4rem;
    letter-spacing: 4px;
  }

  .kdm-premium-subtitle {
    font-size: 1.2rem;
    letter-spacing: 1.5px;
  }
}
